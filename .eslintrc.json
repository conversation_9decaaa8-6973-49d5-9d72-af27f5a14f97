{"extends": ["next/core-web-vitals"], "plugins": ["@typescript-eslint"], "parser": "@typescript-eslint/parser", "parserOptions": {"project": "./tsconfig.json"}, "rules": {"@typescript-eslint/no-unused-vars": "warn", "@typescript-eslint/no-explicit-any": "warn", "@typescript-eslint/prefer-nullish-coalescing": "warn", "@typescript-eslint/prefer-optional-chain": "warn", "@typescript-eslint/no-non-null-assertion": "warn", "import/no-unresolved": "off", "import/named": "off", "no-console": "warn", "react-hooks/exhaustive-deps": "warn", "prefer-const": "warn", "no-var": "warn", "object-shorthand": "warn", "prefer-template": "warn"}}