# Hydration Issues Report

Generated on: 2025-07-22T17:12:23.670Z

## Summary

- **Total Issues**: 279
- **High Severity**: 244
- **Medium Severity**: 35
- **Low Severity**: 0

## Issues by Severity

### 🔴 High Severity Issues (244)


**Math.random() usage**
- File: `src/app/api/admin/performance/route.ts:107`
- Code: `Math.random()`
- Description: Math.random() generates different values on server vs client
- Fix: Replace with deterministic calculations or use useEffect


**Math.random() usage**
- File: `src/app/api/admin/performance/route.ts:107`
- Code: `Math.random()`
- Description: Math.random() generates different values on server vs client
- Fix: Replace with deterministic calculations or use useEffect


**Math.random() usage**
- File: `src/app/api/admin/performance/route.ts:107`
- Code: `Math.random()`
- Description: Math.random() generates different values on server vs client
- Fix: Replace with deterministic calculations or use useEffect


**Math.random() usage**
- File: `src/app/api/admin/performance/route.ts:107`
- Code: `Math.random()`
- Description: Math.random() generates different values on server vs client
- Fix: Replace with deterministic calculations or use useEffect


**Math.random() usage**
- File: `src/app/api/admin/performance/route.ts:107`
- Code: `Math.random()`
- Description: Math.random() generates different values on server vs client
- Fix: Replace with deterministic calculations or use useEffect


**Math.random() usage**
- File: `src/app/api/admin/performance/route.ts:107`
- Code: `Math.random()`
- Description: Math.random() generates different values on server vs client
- Fix: Replace with deterministic calculations or use useEffect


**Math.random() usage**
- File: `src/app/api/admin/performance/route.ts:107`
- Code: `Math.random()`
- Description: Math.random() generates different values on server vs client
- Fix: Replace with deterministic calculations or use useEffect


**Date.now() usage**
- File: `src/app/api/admin/performance/route.ts:17`
- Code: `Date.now()`
- Description: Date.now() returns different values on server vs client
- Fix: Use static dates or client-side only calculations


**Date.now() usage**
- File: `src/app/api/admin/performance/route.ts:17`
- Code: `Date.now()`
- Description: Date.now() returns different values on server vs client
- Fix: Use static dates or client-side only calculations


**Date.now() usage**
- File: `src/app/api/health/db/route.ts:25`
- Code: `Date.now()`
- Description: Date.now() returns different values on server vs client
- Fix: Use static dates or client-side only calculations


**Date.now() usage**
- File: `src/app/api/health/db/route.ts:25`
- Code: `Date.now()`
- Description: Date.now() returns different values on server vs client
- Fix: Use static dates or client-side only calculations


**Date.now() usage**
- File: `src/app/api/health/db/route.ts:25`
- Code: `Date.now()`
- Description: Date.now() returns different values on server vs client
- Fix: Use static dates or client-side only calculations


**Date.now() usage**
- File: `src/app/api/health/route.ts:34`
- Code: `Date.now()`
- Description: Date.now() returns different values on server vs client
- Fix: Use static dates or client-side only calculations


**Date.now() usage**
- File: `src/app/api/health/route.ts:34`
- Code: `Date.now()`
- Description: Date.now() returns different values on server vs client
- Fix: Use static dates or client-side only calculations


**Date.now() usage**
- File: `src/app/api/health/route.ts:34`
- Code: `Date.now()`
- Description: Date.now() returns different values on server vs client
- Fix: Use static dates or client-side only calculations


**Date.now() usage**
- File: `src/app/api/health/route.ts:34`
- Code: `Date.now()`
- Description: Date.now() returns different values on server vs client
- Fix: Use static dates or client-side only calculations


**Date.now() usage**
- File: `src/app/api/health/route.ts:34`
- Code: `Date.now()`
- Description: Date.now() returns different values on server vs client
- Fix: Use static dates or client-side only calculations


**Date.now() usage**
- File: `src/app/api/invoices/route.ts:56`
- Code: `Date.now()`
- Description: Date.now() returns different values on server vs client
- Fix: Use static dates or client-side only calculations


**new Date() without parameters**
- File: `src/app/dashboard/calendar/page.tsx:52`
- Code: `new Date()`
- Description: new Date() returns different values on server vs client
- Fix: Use static dates or client-side only calculations


**new Date() without parameters**
- File: `src/app/dashboard/clients/page.tsx:194`
- Code: `new Date()`
- Description: new Date() returns different values on server vs client
- Fix: Use static dates or client-side only calculations


**window object usage**
- File: `src/app/dashboard/product/page.tsx:39`
- Code: `window.`
- Description: window object is not available during SSR
- Fix: Add typeof window !== "undefined" check or use useEffect


**Date.now() usage**
- File: `src/app/dashboard/settings/appointment-types/page.tsx:146`
- Code: `Date.now()`
- Description: Date.now() returns different values on server vs client
- Fix: Use static dates or client-side only calculations


**localStorage usage**
- File: `src/app/dashboard/settings/appointment-types/page.tsx:86`
- Code: `localStorage.`
- Description: localStorage is not available during SSR
- Fix: Add typeof window !== "undefined" check or use useEffect


**localStorage usage**
- File: `src/app/dashboard/settings/appointment-types/page.tsx:86`
- Code: `localStorage.`
- Description: localStorage is not available during SSR
- Fix: Add typeof window !== "undefined" check or use useEffect


**document object usage**
- File: `src/app/dashboard/settings/page.tsx:202`
- Code: `document.`
- Description: document object is not available during SSR
- Fix: Add typeof document !== "undefined" check or use useEffect


**document object usage**
- File: `src/app/dashboard/settings/page.tsx:202`
- Code: `document.`
- Description: document object is not available during SSR
- Fix: Add typeof document !== "undefined" check or use useEffect


**Date.now() usage**
- File: `src/app/dashboard/settings/treatment-categories/page.tsx:147`
- Code: `Date.now()`
- Description: Date.now() returns different values on server vs client
- Fix: Use static dates or client-side only calculations


**localStorage usage**
- File: `src/app/dashboard/settings/treatment-categories/page.tsx:91`
- Code: `localStorage.`
- Description: localStorage is not available during SSR
- Fix: Add typeof window !== "undefined" check or use useEffect


**localStorage usage**
- File: `src/app/dashboard/settings/treatment-categories/page.tsx:91`
- Code: `localStorage.`
- Description: localStorage is not available during SSR
- Fix: Add typeof window !== "undefined" check or use useEffect


**localStorage usage**
- File: `src/app/dashboard/settings/treatment-categories/page.tsx:91`
- Code: `localStorage.`
- Description: localStorage is not available during SSR
- Fix: Add typeof window !== "undefined" check or use useEffect


**window object usage**
- File: `src/app/layout.tsx:43`
- Code: `window.`
- Description: window object is not available during SSR
- Fix: Add typeof window !== "undefined" check or use useEffect


**localStorage usage**
- File: `src/app/layout.tsx:43`
- Code: `localStorage.`
- Description: localStorage is not available during SSR
- Fix: Add typeof window !== "undefined" check or use useEffect


**localStorage usage**
- File: `src/app/layout.tsx:43`
- Code: `localStorage.`
- Description: localStorage is not available during SSR
- Fix: Add typeof window !== "undefined" check or use useEffect


**document object usage**
- File: `src/app/layout.tsx:44`
- Code: `document.`
- Description: document object is not available during SSR
- Fix: Add typeof document !== "undefined" check or use useEffect


**window object usage**
- File: `src/components/active-theme.tsx:17`
- Code: `window.`
- Description: window object is not available during SSR
- Fix: Add typeof window !== "undefined" check or use useEffect


**document object usage**
- File: `src/components/active-theme.tsx:17`
- Code: `document.`
- Description: document object is not available during SSR
- Fix: Add typeof document !== "undefined" check or use useEffect


**document object usage**
- File: `src/components/active-theme.tsx:17`
- Code: `document.`
- Description: document object is not available during SSR
- Fix: Add typeof document !== "undefined" check or use useEffect


**document object usage**
- File: `src/components/active-theme.tsx:17`
- Code: `document.`
- Description: document object is not available during SSR
- Fix: Add typeof document !== "undefined" check or use useEffect


**document object usage**
- File: `src/components/active-theme.tsx:17`
- Code: `document.`
- Description: document object is not available during SSR
- Fix: Add typeof document !== "undefined" check or use useEffect


**document object usage**
- File: `src/components/active-theme.tsx:17`
- Code: `document.`
- Description: document object is not available during SSR
- Fix: Add typeof document !== "undefined" check or use useEffect


**new Date() without parameters**
- File: `src/components/admin/PerformanceDashboard.tsx:74`
- Code: `new Date()`
- Description: new Date() returns different values on server vs client
- Fix: Use static dates or client-side only calculations


**new Date() without parameters**
- File: `src/components/admin/PerformanceDashboard.tsx:74`
- Code: `new Date()`
- Description: new Date() returns different values on server vs client
- Fix: Use static dates or client-side only calculations


**Date.now() usage**
- File: `src/components/admin/SecurityDashboard.tsx:163`
- Code: `Date.now()`
- Description: Date.now() returns different values on server vs client
- Fix: Use static dates or client-side only calculations


**Date.now() usage**
- File: `src/components/admin/SecurityDashboard.tsx:163`
- Code: `Date.now()`
- Description: Date.now() returns different values on server vs client
- Fix: Use static dates or client-side only calculations


**new Date() without parameters**
- File: `src/components/admin/SecurityDashboard.tsx:90`
- Code: `new Date()`
- Description: new Date() returns different values on server vs client
- Fix: Use static dates or client-side only calculations


**new Date() without parameters**
- File: `src/components/admin/SecurityDashboard.tsx:90`
- Code: `new Date()`
- Description: new Date() returns different values on server vs client
- Fix: Use static dates or client-side only calculations


**window object usage**
- File: `src/components/admin/SecurityDashboard.tsx:230`
- Code: `window.`
- Description: window object is not available during SSR
- Fix: Add typeof window !== "undefined" check or use useEffect


**window object usage**
- File: `src/components/admin/SecurityDashboard.tsx:230`
- Code: `window.`
- Description: window object is not available during SSR
- Fix: Add typeof window !== "undefined" check or use useEffect


**document object usage**
- File: `src/components/admin/SecurityDashboard.tsx:231`
- Code: `document.`
- Description: document object is not available during SSR
- Fix: Add typeof document !== "undefined" check or use useEffect


**document object usage**
- File: `src/components/admin/SecurityDashboard.tsx:231`
- Code: `document.`
- Description: document object is not available during SSR
- Fix: Add typeof document !== "undefined" check or use useEffect


**document object usage**
- File: `src/components/admin/SecurityDashboard.tsx:231`
- Code: `document.`
- Description: document object is not available during SSR
- Fix: Add typeof document !== "undefined" check or use useEffect


**new Date() without parameters**
- File: `src/components/analytics/AdvancedAnalyticsDashboard.tsx:69`
- Code: `new Date()`
- Description: new Date() returns different values on server vs client
- Fix: Use static dates or client-side only calculations


**new Date() without parameters**
- File: `src/components/analytics/AdvancedAnalyticsDashboard.tsx:69`
- Code: `new Date()`
- Description: new Date() returns different values on server vs client
- Fix: Use static dates or client-side only calculations


**new Date() without parameters**
- File: `src/components/analytics/AdvancedAnalyticsDashboard.tsx:69`
- Code: `new Date()`
- Description: new Date() returns different values on server vs client
- Fix: Use static dates or client-side only calculations


**Math.random() usage**
- File: `src/components/analytics/BusinessAnalyticsDashboard.tsx:127`
- Code: `Math.random()`
- Description: Math.random() generates different values on server vs client
- Fix: Replace with deterministic calculations or use useEffect


**Math.random() usage**
- File: `src/components/analytics/BusinessAnalyticsDashboard.tsx:127`
- Code: `Math.random()`
- Description: Math.random() generates different values on server vs client
- Fix: Replace with deterministic calculations or use useEffect


**Math.random() usage**
- File: `src/components/analytics/BusinessAnalyticsDashboard.tsx:127`
- Code: `Math.random()`
- Description: Math.random() generates different values on server vs client
- Fix: Replace with deterministic calculations or use useEffect


**new Date() without parameters**
- File: `src/components/analytics/BusinessAnalyticsDashboard.tsx:126`
- Code: `new Date()`
- Description: new Date() returns different values on server vs client
- Fix: Use static dates or client-side only calculations


**new Date() without parameters**
- File: `src/components/appointment/AppointmentReminders.tsx:45`
- Code: `new Date()`
- Description: new Date() returns different values on server vs client
- Fix: Use static dates or client-side only calculations


**Date.now() usage**
- File: `src/components/client/ClientTagManager.tsx:108`
- Code: `Date.now()`
- Description: Date.now() returns different values on server vs client
- Fix: Use static dates or client-side only calculations


**Date.now() usage**
- File: `src/components/dashboard/CRMSummaryCard.tsx:66`
- Code: `Date.now()`
- Description: Date.now() returns different values on server vs client
- Fix: Use static dates or client-side only calculations


**Date.now() usage**
- File: `src/components/dashboard/CRMSummaryCard.tsx:66`
- Code: `Date.now()`
- Description: Date.now() returns different values on server vs client
- Fix: Use static dates or client-side only calculations


**window object usage**
- File: `src/components/dashboard/QuickActionsToolbar.tsx:31`
- Code: `window.`
- Description: window object is not available during SSR
- Fix: Add typeof window !== "undefined" check or use useEffect


**Math.random() usage**
- File: `src/components/finance/FinancialReports.tsx:122`
- Code: `Math.random()`
- Description: Math.random() generates different values on server vs client
- Fix: Replace with deterministic calculations or use useEffect


**Math.random() usage**
- File: `src/components/finance/FinancialReports.tsx:122`
- Code: `Math.random()`
- Description: Math.random() generates different values on server vs client
- Fix: Replace with deterministic calculations or use useEffect


**Math.random() usage**
- File: `src/components/finance/FinancialReports.tsx:122`
- Code: `Math.random()`
- Description: Math.random() generates different values on server vs client
- Fix: Replace with deterministic calculations or use useEffect


**new Date() without parameters**
- File: `src/components/finance/FinancialReports.tsx:87`
- Code: `new Date()`
- Description: new Date() returns different values on server vs client
- Fix: Use static dates or client-side only calculations


**Date.now() usage**
- File: `src/components/finance/PaymentProcessor.tsx:125`
- Code: `Date.now()`
- Description: Date.now() returns different values on server vs client
- Fix: Use static dates or client-side only calculations


**new Date() without parameters**
- File: `src/components/finance/PaymentProcessor.tsx:129`
- Code: `new Date()`
- Description: new Date() returns different values on server vs client
- Fix: Use static dates or client-side only calculations


**window object usage**
- File: `src/components/finance/PaymentProcessor.tsx:161`
- Code: `window.`
- Description: window object is not available during SSR
- Fix: Add typeof window !== "undefined" check or use useEffect


**window object usage**
- File: `src/components/layout/MobileLayout.tsx:106`
- Code: `window.`
- Description: window object is not available during SSR
- Fix: Add typeof window !== "undefined" check or use useEffect


**window object usage**
- File: `src/components/layout/MobileLayout.tsx:106`
- Code: `window.`
- Description: window object is not available during SSR
- Fix: Add typeof window !== "undefined" check or use useEffect


**window object usage**
- File: `src/components/layout/MobileLayout.tsx:106`
- Code: `window.`
- Description: window object is not available during SSR
- Fix: Add typeof window !== "undefined" check or use useEffect


**document object usage**
- File: `src/components/layout/MobileLayout.tsx:122`
- Code: `document.`
- Description: document object is not available during SSR
- Fix: Add typeof document !== "undefined" check or use useEffect


**document object usage**
- File: `src/components/layout/MobileLayout.tsx:122`
- Code: `document.`
- Description: document object is not available during SSR
- Fix: Add typeof document !== "undefined" check or use useEffect


**document object usage**
- File: `src/components/layout/MobileLayout.tsx:122`
- Code: `document.`
- Description: document object is not available during SSR
- Fix: Add typeof document !== "undefined" check or use useEffect


**document object usage**
- File: `src/components/layout/ThemeToggle/theme-toggle.tsx:15`
- Code: `document.`
- Description: document object is not available during SSR
- Fix: Add typeof document !== "undefined" check or use useEffect


**document object usage**
- File: `src/components/layout/ThemeToggle/theme-toggle.tsx:15`
- Code: `document.`
- Description: document object is not available during SSR
- Fix: Add typeof document !== "undefined" check or use useEffect


**document object usage**
- File: `src/components/layout/ThemeToggle/theme-toggle.tsx:15`
- Code: `document.`
- Description: document object is not available during SSR
- Fix: Add typeof document !== "undefined" check or use useEffect


**window object usage**
- File: `src/components/mobile/TouchOptimizedComponents.tsx:275`
- Code: `window.`
- Description: window object is not available during SSR
- Fix: Add typeof window !== "undefined" check or use useEffect


**new Date() without parameters**
- File: `src/components/modals/AppointmentModal.tsx:125`
- Code: `new Date()`
- Description: new Date() returns different values on server vs client
- Fix: Use static dates or client-side only calculations


**new Date() without parameters**
- File: `src/components/modals/AppointmentModal.tsx:125`
- Code: `new Date()`
- Description: new Date() returns different values on server vs client
- Fix: Use static dates or client-side only calculations


**new Date() without parameters**
- File: `src/components/modals/AppointmentModal.tsx:125`
- Code: `new Date()`
- Description: new Date() returns different values on server vs client
- Fix: Use static dates or client-side only calculations


**window object usage**
- File: `src/components/modals/AppointmentModal.tsx:201`
- Code: `window.`
- Description: window object is not available during SSR
- Fix: Add typeof window !== "undefined" check or use useEffect


**new Date() without parameters**
- File: `src/components/modals/InvoiceModal.tsx:117`
- Code: `new Date()`
- Description: new Date() returns different values on server vs client
- Fix: Use static dates or client-side only calculations


**new Date() without parameters**
- File: `src/components/modals/InvoiceModal.tsx:117`
- Code: `new Date()`
- Description: new Date() returns different values on server vs client
- Fix: Use static dates or client-side only calculations


**localStorage usage**
- File: `src/components/ui/appointment-type-select.tsx:57`
- Code: `localStorage.`
- Description: localStorage is not available during SSR
- Fix: Add typeof window !== "undefined" check or use useEffect


**localStorage usage**
- File: `src/components/ui/appointment-type-select.tsx:57`
- Code: `localStorage.`
- Description: localStorage is not available during SSR
- Fix: Add typeof window !== "undefined" check or use useEffect


**localStorage usage**
- File: `src/components/ui/global-search.tsx:67`
- Code: `localStorage.`
- Description: localStorage is not available during SSR
- Fix: Add typeof window !== "undefined" check or use useEffect


**localStorage usage**
- File: `src/components/ui/global-search.tsx:67`
- Code: `localStorage.`
- Description: localStorage is not available during SSR
- Fix: Add typeof window !== "undefined" check or use useEffect


**document object usage**
- File: `src/components/ui/global-search.tsx:61`
- Code: `document.`
- Description: document object is not available during SSR
- Fix: Add typeof document !== "undefined" check or use useEffect


**document object usage**
- File: `src/components/ui/global-search.tsx:61`
- Code: `document.`
- Description: document object is not available during SSR
- Fix: Add typeof document !== "undefined" check or use useEffect


**window object usage**
- File: `src/components/ui/responsive-layout.tsx:28`
- Code: `window.`
- Description: window object is not available during SSR
- Fix: Add typeof window !== "undefined" check or use useEffect


**window object usage**
- File: `src/components/ui/responsive-layout.tsx:28`
- Code: `window.`
- Description: window object is not available during SSR
- Fix: Add typeof window !== "undefined" check or use useEffect


**window object usage**
- File: `src/components/ui/responsive-layout.tsx:28`
- Code: `window.`
- Description: window object is not available during SSR
- Fix: Add typeof window !== "undefined" check or use useEffect


**Math.random() usage**
- File: `src/components/ui/sidebar.tsx:610`
- Code: `Math.random()`
- Description: Math.random() generates different values on server vs client
- Fix: Replace with deterministic calculations or use useEffect


**window object usage**
- File: `src/components/ui/sidebar.tsx:107`
- Code: `window.`
- Description: window object is not available during SSR
- Fix: Add typeof window !== "undefined" check or use useEffect


**window object usage**
- File: `src/components/ui/sidebar.tsx:107`
- Code: `window.`
- Description: window object is not available during SSR
- Fix: Add typeof window !== "undefined" check or use useEffect


**document object usage**
- File: `src/components/ui/sidebar.tsx:85`
- Code: `document.`
- Description: document object is not available during SSR
- Fix: Add typeof document !== "undefined" check or use useEffect


**document object usage**
- File: `src/features/kanban/components/column-action.tsx:103`
- Code: `document.`
- Description: document object is not available during SSR
- Fix: Add typeof document !== "undefined" check or use useEffect


**document object usage**
- File: `src/features/kanban/components/kanban-board.tsx:222`
- Code: `document.`
- Description: document object is not available during SSR
- Fix: Add typeof document !== "undefined" check or use useEffect


**Math.random() usage**
- File: `src/features/overview/components/bar-graph-skeleton.tsx:32`
- Code: `Math.random()`
- Description: Math.random() generates different values on server vs client
- Fix: Replace with deterministic calculations or use useEffect


**window object usage**
- File: `src/hooks/use-debounced-callback.ts:12`
- Code: `window.`
- Description: window object is not available during SSR
- Fix: Add typeof window !== "undefined" check or use useEffect


**window object usage**
- File: `src/hooks/use-debounced-callback.ts:12`
- Code: `window.`
- Description: window object is not available during SSR
- Fix: Add typeof window !== "undefined" check or use useEffect


**window object usage**
- File: `src/hooks/use-debounced-callback.ts:12`
- Code: `window.`
- Description: window object is not available during SSR
- Fix: Add typeof window !== "undefined" check or use useEffect


**window object usage**
- File: `src/hooks/use-media-query.ts:7`
- Code: `window.`
- Description: window object is not available during SSR
- Fix: Add typeof window !== "undefined" check or use useEffect


**window object usage**
- File: `src/hooks/use-mobile.tsx:11`
- Code: `window.`
- Description: window object is not available during SSR
- Fix: Add typeof window !== "undefined" check or use useEffect


**window object usage**
- File: `src/hooks/use-mobile.tsx:11`
- Code: `window.`
- Description: window object is not available during SSR
- Fix: Add typeof window !== "undefined" check or use useEffect


**window object usage**
- File: `src/hooks/use-mobile.tsx:11`
- Code: `window.`
- Description: window object is not available during SSR
- Fix: Add typeof window !== "undefined" check or use useEffect


**Date.now() usage**
- File: `src/lib/ai/intelligent-automation.ts:544`
- Code: `Date.now()`
- Description: Date.now() returns different values on server vs client
- Fix: Use static dates or client-side only calculations


**Date.now() usage**
- File: `src/lib/ai/intelligent-automation.ts:544`
- Code: `Date.now()`
- Description: Date.now() returns different values on server vs client
- Fix: Use static dates or client-side only calculations


**Date.now() usage**
- File: `src/lib/ai/intelligent-automation.ts:544`
- Code: `Date.now()`
- Description: Date.now() returns different values on server vs client
- Fix: Use static dates or client-side only calculations


**new Date() without parameters**
- File: `src/lib/ai/intelligent-automation.ts:363`
- Code: `new Date()`
- Description: new Date() returns different values on server vs client
- Fix: Use static dates or client-side only calculations


**Date.now() usage**
- File: `src/lib/analytics/business-intelligence.ts:83`
- Code: `Date.now()`
- Description: Date.now() returns different values on server vs client
- Fix: Use static dates or client-side only calculations


**Date.now() usage**
- File: `src/lib/analytics/business-intelligence.ts:83`
- Code: `Date.now()`
- Description: Date.now() returns different values on server vs client
- Fix: Use static dates or client-side only calculations


**Date.now() usage**
- File: `src/lib/analytics/business-intelligence.ts:83`
- Code: `Date.now()`
- Description: Date.now() returns different values on server vs client
- Fix: Use static dates or client-side only calculations


**new Date() without parameters**
- File: `src/lib/analytics/business-intelligence.ts:113`
- Code: `new Date()`
- Description: new Date() returns different values on server vs client
- Fix: Use static dates or client-side only calculations


**Math.random() usage**
- File: `src/lib/auth/rbac.ts:266`
- Code: `Math.random()`
- Description: Math.random() generates different values on server vs client
- Fix: Replace with deterministic calculations or use useEffect


**Math.random() usage**
- File: `src/lib/auth/rbac.ts:266`
- Code: `Math.random()`
- Description: Math.random() generates different values on server vs client
- Fix: Replace with deterministic calculations or use useEffect


**Date.now() usage**
- File: `src/lib/auth/rbac.ts:266`
- Code: `Date.now()`
- Description: Date.now() returns different values on server vs client
- Fix: Use static dates or client-side only calculations


**Date.now() usage**
- File: `src/lib/auth/rbac.ts:266`
- Code: `Date.now()`
- Description: Date.now() returns different values on server vs client
- Fix: Use static dates or client-side only calculations


**Date.now() usage**
- File: `src/lib/auth/rbac.ts:266`
- Code: `Date.now()`
- Description: Date.now() returns different values on server vs client
- Fix: Use static dates or client-side only calculations


**Date.now() usage**
- File: `src/lib/auth/rbac.ts:266`
- Code: `Date.now()`
- Description: Date.now() returns different values on server vs client
- Fix: Use static dates or client-side only calculations


**new Date() without parameters**
- File: `src/lib/auth/rbac.ts:250`
- Code: `new Date()`
- Description: new Date() returns different values on server vs client
- Fix: Use static dates or client-side only calculations


**new Date() without parameters**
- File: `src/lib/auth/rbac.ts:250`
- Code: `new Date()`
- Description: new Date() returns different values on server vs client
- Fix: Use static dates or client-side only calculations


**new Date() without parameters**
- File: `src/lib/auth/rbac.ts:250`
- Code: `new Date()`
- Description: new Date() returns different values on server vs client
- Fix: Use static dates or client-side only calculations


**new Date() without parameters**
- File: `src/lib/auth/rbac.ts:250`
- Code: `new Date()`
- Description: new Date() returns different values on server vs client
- Fix: Use static dates or client-side only calculations


**new Date() without parameters**
- File: `src/lib/auth/rbac.ts:250`
- Code: `new Date()`
- Description: new Date() returns different values on server vs client
- Fix: Use static dates or client-side only calculations


**new Date() without parameters**
- File: `src/lib/auth/rbac.ts:250`
- Code: `new Date()`
- Description: new Date() returns different values on server vs client
- Fix: Use static dates or client-side only calculations


**new Date() without parameters**
- File: `src/lib/auth/rbac.ts:250`
- Code: `new Date()`
- Description: new Date() returns different values on server vs client
- Fix: Use static dates or client-side only calculations


**new Date() without parameters**
- File: `src/lib/auth/rbac.ts:250`
- Code: `new Date()`
- Description: new Date() returns different values on server vs client
- Fix: Use static dates or client-side only calculations


**new Date() without parameters**
- File: `src/lib/auth/rbac.ts:250`
- Code: `new Date()`
- Description: new Date() returns different values on server vs client
- Fix: Use static dates or client-side only calculations


**new Date() without parameters**
- File: `src/lib/auth/rbac.ts:250`
- Code: `new Date()`
- Description: new Date() returns different values on server vs client
- Fix: Use static dates or client-side only calculations


**new Date() without parameters**
- File: `src/lib/auth/rbac.ts:250`
- Code: `new Date()`
- Description: new Date() returns different values on server vs client
- Fix: Use static dates or client-side only calculations


**Date.now() usage**
- File: `src/lib/cache.ts:84`
- Code: `Date.now()`
- Description: Date.now() returns different values on server vs client
- Fix: Use static dates or client-side only calculations


**Date.now() usage**
- File: `src/lib/cache.ts:84`
- Code: `Date.now()`
- Description: Date.now() returns different values on server vs client
- Fix: Use static dates or client-side only calculations


**Date.now() usage**
- File: `src/lib/cache.ts:84`
- Code: `Date.now()`
- Description: Date.now() returns different values on server vs client
- Fix: Use static dates or client-side only calculations


**Date.now() usage**
- File: `src/lib/cache.ts:84`
- Code: `Date.now()`
- Description: Date.now() returns different values on server vs client
- Fix: Use static dates or client-side only calculations


**Date.now() usage**
- File: `src/lib/cache.ts:84`
- Code: `Date.now()`
- Description: Date.now() returns different values on server vs client
- Fix: Use static dates or client-side only calculations


**Math.random() usage**
- File: `src/lib/compliance/automation.ts:270`
- Code: `Math.random()`
- Description: Math.random() generates different values on server vs client
- Fix: Replace with deterministic calculations or use useEffect


**Date.now() usage**
- File: `src/lib/compliance/automation.ts:270`
- Code: `Date.now()`
- Description: Date.now() returns different values on server vs client
- Fix: Use static dates or client-side only calculations


**Date.now() usage**
- File: `src/lib/compliance/automation.ts:270`
- Code: `Date.now()`
- Description: Date.now() returns different values on server vs client
- Fix: Use static dates or client-side only calculations


**Date.now() usage**
- File: `src/lib/compliance/automation.ts:270`
- Code: `Date.now()`
- Description: Date.now() returns different values on server vs client
- Fix: Use static dates or client-side only calculations


**Date.now() usage**
- File: `src/lib/compliance/automation.ts:270`
- Code: `Date.now()`
- Description: Date.now() returns different values on server vs client
- Fix: Use static dates or client-side only calculations


**new Date() without parameters**
- File: `src/lib/compliance/automation.ts:170`
- Code: `new Date()`
- Description: new Date() returns different values on server vs client
- Fix: Use static dates or client-side only calculations


**new Date() without parameters**
- File: `src/lib/compliance/automation.ts:170`
- Code: `new Date()`
- Description: new Date() returns different values on server vs client
- Fix: Use static dates or client-side only calculations


**new Date() without parameters**
- File: `src/lib/compliance/automation.ts:170`
- Code: `new Date()`
- Description: new Date() returns different values on server vs client
- Fix: Use static dates or client-side only calculations


**new Date() without parameters**
- File: `src/lib/compliance/automation.ts:170`
- Code: `new Date()`
- Description: new Date() returns different values on server vs client
- Fix: Use static dates or client-side only calculations


**new Date() without parameters**
- File: `src/lib/compliance/automation.ts:170`
- Code: `new Date()`
- Description: new Date() returns different values on server vs client
- Fix: Use static dates or client-side only calculations


**Date.now() usage**
- File: `src/lib/compliance/hipaa.ts:506`
- Code: `Date.now()`
- Description: Date.now() returns different values on server vs client
- Fix: Use static dates or client-side only calculations


**new Date() without parameters**
- File: `src/lib/compliance/hipaa.ts:301`
- Code: `new Date()`
- Description: new Date() returns different values on server vs client
- Fix: Use static dates or client-side only calculations


**document object usage**
- File: `src/lib/data-export.ts:70`
- Code: `document.`
- Description: document object is not available during SSR
- Fix: Add typeof document !== "undefined" check or use useEffect


**document object usage**
- File: `src/lib/data-export.ts:70`
- Code: `document.`
- Description: document object is not available during SSR
- Fix: Add typeof document !== "undefined" check or use useEffect


**document object usage**
- File: `src/lib/data-export.ts:70`
- Code: `document.`
- Description: document object is not available during SSR
- Fix: Add typeof document !== "undefined" check or use useEffect


**Date.now() usage**
- File: `src/lib/database/performance.ts:138`
- Code: `Date.now()`
- Description: Date.now() returns different values on server vs client
- Fix: Use static dates or client-side only calculations


**Date.now() usage**
- File: `src/lib/database/performance.ts:138`
- Code: `Date.now()`
- Description: Date.now() returns different values on server vs client
- Fix: Use static dates or client-side only calculations


**Date.now() usage**
- File: `src/lib/database/performance.ts:138`
- Code: `Date.now()`
- Description: Date.now() returns different values on server vs client
- Fix: Use static dates or client-side only calculations


**Date.now() usage**
- File: `src/lib/database/performance.ts:138`
- Code: `Date.now()`
- Description: Date.now() returns different values on server vs client
- Fix: Use static dates or client-side only calculations


**Date.now() usage**
- File: `src/lib/database/performance.ts:138`
- Code: `Date.now()`
- Description: Date.now() returns different values on server vs client
- Fix: Use static dates or client-side only calculations


**Date.now() usage**
- File: `src/lib/database/performance.ts:138`
- Code: `Date.now()`
- Description: Date.now() returns different values on server vs client
- Fix: Use static dates or client-side only calculations


**window object usage**
- File: `src/lib/google-maps.ts:40`
- Code: `window.`
- Description: window object is not available during SSR
- Fix: Add typeof window !== "undefined" check or use useEffect


**window object usage**
- File: `src/lib/google-maps.ts:40`
- Code: `window.`
- Description: window object is not available during SSR
- Fix: Add typeof window !== "undefined" check or use useEffect


**window object usage**
- File: `src/lib/google-maps.ts:40`
- Code: `window.`
- Description: window object is not available during SSR
- Fix: Add typeof window !== "undefined" check or use useEffect


**window object usage**
- File: `src/lib/google-maps.ts:40`
- Code: `window.`
- Description: window object is not available during SSR
- Fix: Add typeof window !== "undefined" check or use useEffect


**document object usage**
- File: `src/lib/google-maps.ts:51`
- Code: `document.`
- Description: document object is not available during SSR
- Fix: Add typeof document !== "undefined" check or use useEffect


**document object usage**
- File: `src/lib/google-maps.ts:51`
- Code: `document.`
- Description: document object is not available during SSR
- Fix: Add typeof document !== "undefined" check or use useEffect


**document object usage**
- File: `src/lib/google-maps.ts:51`
- Code: `document.`
- Description: document object is not available during SSR
- Fix: Add typeof document !== "undefined" check or use useEffect


**Math.random() usage**
- File: `src/lib/integrations/payment-gateways.ts:293`
- Code: `Math.random()`
- Description: Math.random() generates different values on server vs client
- Fix: Replace with deterministic calculations or use useEffect


**Math.random() usage**
- File: `src/lib/integrations/payment-gateways.ts:293`
- Code: `Math.random()`
- Description: Math.random() generates different values on server vs client
- Fix: Replace with deterministic calculations or use useEffect


**Math.random() usage**
- File: `src/lib/integrations/payment-gateways.ts:293`
- Code: `Math.random()`
- Description: Math.random() generates different values on server vs client
- Fix: Replace with deterministic calculations or use useEffect


**Math.random() usage**
- File: `src/lib/integrations/payment-gateways.ts:293`
- Code: `Math.random()`
- Description: Math.random() generates different values on server vs client
- Fix: Replace with deterministic calculations or use useEffect


**Math.random() usage**
- File: `src/lib/integrations/payment-gateways.ts:293`
- Code: `Math.random()`
- Description: Math.random() generates different values on server vs client
- Fix: Replace with deterministic calculations or use useEffect


**Math.random() usage**
- File: `src/lib/integrations/payment-gateways.ts:293`
- Code: `Math.random()`
- Description: Math.random() generates different values on server vs client
- Fix: Replace with deterministic calculations or use useEffect


**Date.now() usage**
- File: `src/lib/integrations/payment-gateways.ts:293`
- Code: `Date.now()`
- Description: Date.now() returns different values on server vs client
- Fix: Use static dates or client-side only calculations


**Date.now() usage**
- File: `src/lib/integrations/payment-gateways.ts:293`
- Code: `Date.now()`
- Description: Date.now() returns different values on server vs client
- Fix: Use static dates or client-side only calculations


**Date.now() usage**
- File: `src/lib/integrations/payment-gateways.ts:293`
- Code: `Date.now()`
- Description: Date.now() returns different values on server vs client
- Fix: Use static dates or client-side only calculations


**Date.now() usage**
- File: `src/lib/integrations/payment-gateways.ts:293`
- Code: `Date.now()`
- Description: Date.now() returns different values on server vs client
- Fix: Use static dates or client-side only calculations


**Date.now() usage**
- File: `src/lib/integrations/payment-gateways.ts:293`
- Code: `Date.now()`
- Description: Date.now() returns different values on server vs client
- Fix: Use static dates or client-side only calculations


**Date.now() usage**
- File: `src/lib/integrations/payment-gateways.ts:293`
- Code: `Date.now()`
- Description: Date.now() returns different values on server vs client
- Fix: Use static dates or client-side only calculations


**Date.now() usage**
- File: `src/lib/integrations/payment-gateways.ts:293`
- Code: `Date.now()`
- Description: Date.now() returns different values on server vs client
- Fix: Use static dates or client-side only calculations


**Date.now() usage**
- File: `src/lib/integrations/payment-gateways.ts:293`
- Code: `Date.now()`
- Description: Date.now() returns different values on server vs client
- Fix: Use static dates or client-side only calculations


**Date.now() usage**
- File: `src/lib/integrations/payment-gateways.ts:293`
- Code: `Date.now()`
- Description: Date.now() returns different values on server vs client
- Fix: Use static dates or client-side only calculations


**Date.now() usage**
- File: `src/lib/integrations/payment-gateways.ts:293`
- Code: `Date.now()`
- Description: Date.now() returns different values on server vs client
- Fix: Use static dates or client-side only calculations


**Date.now() usage**
- File: `src/lib/integrations/payment-gateways.ts:293`
- Code: `Date.now()`
- Description: Date.now() returns different values on server vs client
- Fix: Use static dates or client-side only calculations


**Date.now() usage**
- File: `src/lib/integrations/payment-gateways.ts:293`
- Code: `Date.now()`
- Description: Date.now() returns different values on server vs client
- Fix: Use static dates or client-side only calculations


**Date.now() usage**
- File: `src/lib/integrations/payment-gateways.ts:293`
- Code: `Date.now()`
- Description: Date.now() returns different values on server vs client
- Fix: Use static dates or client-side only calculations


**Date.now() usage**
- File: `src/lib/integrations/payment-gateways.ts:293`
- Code: `Date.now()`
- Description: Date.now() returns different values on server vs client
- Fix: Use static dates or client-side only calculations


**Date.now() usage**
- File: `src/lib/integrations/payment-gateways.ts:293`
- Code: `Date.now()`
- Description: Date.now() returns different values on server vs client
- Fix: Use static dates or client-side only calculations


**Date.now() usage**
- File: `src/lib/integrations/payment-gateways.ts:293`
- Code: `Date.now()`
- Description: Date.now() returns different values on server vs client
- Fix: Use static dates or client-side only calculations


**Date.now() usage**
- File: `src/lib/integrations/payment-gateways.ts:293`
- Code: `Date.now()`
- Description: Date.now() returns different values on server vs client
- Fix: Use static dates or client-side only calculations


**Date.now() usage**
- File: `src/lib/integrations/payment-gateways.ts:293`
- Code: `Date.now()`
- Description: Date.now() returns different values on server vs client
- Fix: Use static dates or client-side only calculations


**new Date() without parameters**
- File: `src/lib/integrations/payment-gateways.ts:268`
- Code: `new Date()`
- Description: new Date() returns different values on server vs client
- Fix: Use static dates or client-side only calculations


**new Date() without parameters**
- File: `src/lib/integrations/payment-gateways.ts:268`
- Code: `new Date()`
- Description: new Date() returns different values on server vs client
- Fix: Use static dates or client-side only calculations


**new Date() without parameters**
- File: `src/lib/integrations/payment-gateways.ts:268`
- Code: `new Date()`
- Description: new Date() returns different values on server vs client
- Fix: Use static dates or client-side only calculations


**new Date() without parameters**
- File: `src/lib/integrations/payment-gateways.ts:268`
- Code: `new Date()`
- Description: new Date() returns different values on server vs client
- Fix: Use static dates or client-side only calculations


**new Date() without parameters**
- File: `src/lib/integrations/payment-gateways.ts:268`
- Code: `new Date()`
- Description: new Date() returns different values on server vs client
- Fix: Use static dates or client-side only calculations


**new Date() without parameters**
- File: `src/lib/integrations/payment-gateways.ts:268`
- Code: `new Date()`
- Description: new Date() returns different values on server vs client
- Fix: Use static dates or client-side only calculations


**new Date() without parameters**
- File: `src/lib/integrations/payment-gateways.ts:268`
- Code: `new Date()`
- Description: new Date() returns different values on server vs client
- Fix: Use static dates or client-side only calculations


**new Date() without parameters**
- File: `src/lib/integrations/payment-gateways.ts:268`
- Code: `new Date()`
- Description: new Date() returns different values on server vs client
- Fix: Use static dates or client-side only calculations


**new Date() without parameters**
- File: `src/lib/integrations/payment-gateways.ts:268`
- Code: `new Date()`
- Description: new Date() returns different values on server vs client
- Fix: Use static dates or client-side only calculations


**new Date() without parameters**
- File: `src/lib/integrations/payment-gateways.ts:268`
- Code: `new Date()`
- Description: new Date() returns different values on server vs client
- Fix: Use static dates or client-side only calculations


**new Date() without parameters**
- File: `src/lib/integrations/payment-gateways.ts:268`
- Code: `new Date()`
- Description: new Date() returns different values on server vs client
- Fix: Use static dates or client-side only calculations


**new Date() without parameters**
- File: `src/lib/integrations/payment-gateways.ts:268`
- Code: `new Date()`
- Description: new Date() returns different values on server vs client
- Fix: Use static dates or client-side only calculations


**new Date() without parameters**
- File: `src/lib/integrations/payment-gateways.ts:268`
- Code: `new Date()`
- Description: new Date() returns different values on server vs client
- Fix: Use static dates or client-side only calculations


**new Date() without parameters**
- File: `src/lib/integrations/payment-gateways.ts:268`
- Code: `new Date()`
- Description: new Date() returns different values on server vs client
- Fix: Use static dates or client-side only calculations


**new Date() without parameters**
- File: `src/lib/integrations/payment-gateways.ts:268`
- Code: `new Date()`
- Description: new Date() returns different values on server vs client
- Fix: Use static dates or client-side only calculations


**new Date() without parameters**
- File: `src/lib/integrations/payment-gateways.ts:268`
- Code: `new Date()`
- Description: new Date() returns different values on server vs client
- Fix: Use static dates or client-side only calculations


**new Date() without parameters**
- File: `src/lib/integrations/payment-gateways.ts:268`
- Code: `new Date()`
- Description: new Date() returns different values on server vs client
- Fix: Use static dates or client-side only calculations


**new Date() without parameters**
- File: `src/lib/integrations/payment-gateways.ts:268`
- Code: `new Date()`
- Description: new Date() returns different values on server vs client
- Fix: Use static dates or client-side only calculations


**new Date() without parameters**
- File: `src/lib/integrations/payment-gateways.ts:268`
- Code: `new Date()`
- Description: new Date() returns different values on server vs client
- Fix: Use static dates or client-side only calculations


**Date.now() usage**
- File: `src/lib/logger.ts:297`
- Code: `Date.now()`
- Description: Date.now() returns different values on server vs client
- Fix: Use static dates or client-side only calculations


**Date.now() usage**
- File: `src/lib/logger.ts:297`
- Code: `Date.now()`
- Description: Date.now() returns different values on server vs client
- Fix: Use static dates or client-side only calculations


**Date.now() usage**
- File: `src/lib/logger.ts:297`
- Code: `Date.now()`
- Description: Date.now() returns different values on server vs client
- Fix: Use static dates or client-side only calculations


**Date.now() usage**
- File: `src/lib/monitoring.ts:70`
- Code: `Date.now()`
- Description: Date.now() returns different values on server vs client
- Fix: Use static dates or client-side only calculations


**Date.now() usage**
- File: `src/lib/monitoring.ts:70`
- Code: `Date.now()`
- Description: Date.now() returns different values on server vs client
- Fix: Use static dates or client-side only calculations


**Date.now() usage**
- File: `src/lib/monitoring.ts:70`
- Code: `Date.now()`
- Description: Date.now() returns different values on server vs client
- Fix: Use static dates or client-side only calculations


**Date.now() usage**
- File: `src/lib/monitoring.ts:70`
- Code: `Date.now()`
- Description: Date.now() returns different values on server vs client
- Fix: Use static dates or client-side only calculations


**Date.now() usage**
- File: `src/lib/monitoring.ts:70`
- Code: `Date.now()`
- Description: Date.now() returns different values on server vs client
- Fix: Use static dates or client-side only calculations


**Date.now() usage**
- File: `src/lib/monitoring.ts:70`
- Code: `Date.now()`
- Description: Date.now() returns different values on server vs client
- Fix: Use static dates or client-side only calculations


**Date.now() usage**
- File: `src/lib/monitoring.ts:70`
- Code: `Date.now()`
- Description: Date.now() returns different values on server vs client
- Fix: Use static dates or client-side only calculations


**Date.now() usage**
- File: `src/lib/monitoring.ts:70`
- Code: `Date.now()`
- Description: Date.now() returns different values on server vs client
- Fix: Use static dates or client-side only calculations


**Date.now() usage**
- File: `src/lib/monitoring.ts:70`
- Code: `Date.now()`
- Description: Date.now() returns different values on server vs client
- Fix: Use static dates or client-side only calculations


**Date.now() usage**
- File: `src/lib/monitoring.ts:70`
- Code: `Date.now()`
- Description: Date.now() returns different values on server vs client
- Fix: Use static dates or client-side only calculations


**Date.now() usage**
- File: `src/lib/react-query.ts:167`
- Code: `Date.now()`
- Description: Date.now() returns different values on server vs client
- Fix: Use static dates or client-side only calculations


**Date.now() usage**
- File: `src/lib/react-query.ts:167`
- Code: `Date.now()`
- Description: Date.now() returns different values on server vs client
- Fix: Use static dates or client-side only calculations


**Date.now() usage**
- File: `src/lib/react-query.ts:167`
- Code: `Date.now()`
- Description: Date.now() returns different values on server vs client
- Fix: Use static dates or client-side only calculations


**Math.random() usage**
- File: `src/lib/security/monitoring.ts:255`
- Code: `Math.random()`
- Description: Math.random() generates different values on server vs client
- Fix: Replace with deterministic calculations or use useEffect


**Math.random() usage**
- File: `src/lib/security/monitoring.ts:255`
- Code: `Math.random()`
- Description: Math.random() generates different values on server vs client
- Fix: Replace with deterministic calculations or use useEffect


**Date.now() usage**
- File: `src/lib/security/monitoring.ts:255`
- Code: `Date.now()`
- Description: Date.now() returns different values on server vs client
- Fix: Use static dates or client-side only calculations


**Date.now() usage**
- File: `src/lib/security/monitoring.ts:255`
- Code: `Date.now()`
- Description: Date.now() returns different values on server vs client
- Fix: Use static dates or client-side only calculations


**Date.now() usage**
- File: `src/lib/security/monitoring.ts:255`
- Code: `Date.now()`
- Description: Date.now() returns different values on server vs client
- Fix: Use static dates or client-side only calculations


**new Date() without parameters**
- File: `src/lib/security/monitoring.ts:258`
- Code: `new Date()`
- Description: new Date() returns different values on server vs client
- Fix: Use static dates or client-side only calculations


**new Date() without parameters**
- File: `src/lib/security/monitoring.ts:258`
- Code: `new Date()`
- Description: new Date() returns different values on server vs client
- Fix: Use static dates or client-side only calculations


**new Date() without parameters**
- File: `src/lib/security/monitoring.ts:258`
- Code: `new Date()`
- Description: new Date() returns different values on server vs client
- Fix: Use static dates or client-side only calculations


**new Date() without parameters**
- File: `src/lib/security/monitoring.ts:258`
- Code: `new Date()`
- Description: new Date() returns different values on server vs client
- Fix: Use static dates or client-side only calculations


**Date.now() usage**
- File: `src/lib/security.ts:128`
- Code: `Date.now()`
- Description: Date.now() returns different values on server vs client
- Fix: Use static dates or client-side only calculations


**Date.now() usage**
- File: `src/lib/security.ts:128`
- Code: `Date.now()`
- Description: Date.now() returns different values on server vs client
- Fix: Use static dates or client-side only calculations


**Date.now() usage**
- File: `src/lib/security.ts:128`
- Code: `Date.now()`
- Description: Date.now() returns different values on server vs client
- Fix: Use static dates or client-side only calculations


**Date.now() usage**
- File: `src/lib/supabase/queries.ts:610`
- Code: `Date.now()`
- Description: Date.now() returns different values on server vs client
- Fix: Use static dates or client-side only calculations


**Date.now() usage**
- File: `src/lib/supabase/queries.ts:610`
- Code: `Date.now()`
- Description: Date.now() returns different values on server vs client
- Fix: Use static dates or client-side only calculations


**Date.now() usage**
- File: `src/lib/supabase/queries.ts:610`
- Code: `Date.now()`
- Description: Date.now() returns different values on server vs client
- Fix: Use static dates or client-side only calculations


**new Date() without parameters**
- File: `src/lib/supabase/queries.ts:155`
- Code: `new Date()`
- Description: new Date() returns different values on server vs client
- Fix: Use static dates or client-side only calculations


**localStorage usage**
- File: `src/lib/working-hours.ts:30`
- Code: `localStorage.`
- Description: localStorage is not available during SSR
- Fix: Add typeof window !== "undefined" check or use useEffect


**localStorage usage**
- File: `src/lib/working-hours.ts:30`
- Code: `localStorage.`
- Description: localStorage is not available during SSR
- Fix: Add typeof window !== "undefined" check or use useEffect


### 🟡 Medium Severity Issues (35)


**toLocaleDateString without options**
- File: `src/app/dashboard/invoices/page.tsx:144`
- Code: `.toLocaleDateString('zh-CN')`
- Description: toLocaleDateString may render differently due to timezone differences
- Fix: Use consistent date formatting with explicit options


**toLocaleDateString without options**
- File: `src/app/dashboard/payments/page.tsx:150`
- Code: `.toLocaleDateString('zh-CN')`
- Description: toLocaleDateString may render differently due to timezone differences
- Fix: Use consistent date formatting with explicit options


**toLocaleDateString without options**
- File: `src/app/dashboard/settings/appointment-types/page.tsx:327`
- Code: `.toLocaleDateString('zh-CN')`
- Description: toLocaleDateString may render differently due to timezone differences
- Fix: Use consistent date formatting with explicit options


**toLocaleDateString without options**
- File: `src/app/dashboard/settings/treatment-categories/page.tsx:304`
- Code: `.toLocaleDateString('zh-CN')`
- Description: toLocaleDateString may render differently due to timezone differences
- Fix: Use consistent date formatting with explicit options


**Dynamic class names without guards**
- File: `src/components/client/ClientPreferences.tsx:0`
- Code: `className={
                      formData.skin_sensitivity_level === level.value 
                        ? level.color 
                        :`
- Description: Dynamic class names may cause hydration mismatches
- Fix: Ensure consistent class name generation or use client-side only


**toLocaleDateString without options**
- File: `src/components/dashboard/CRMSummaryCard.tsx:275`
- Code: `.toLocaleDateString('zh-CN')`
- Description: toLocaleDateString may render differently due to timezone differences
- Fix: Use consistent date formatting with explicit options


**Dynamic class names without guards**
- File: `src/components/layout/MobileLayout.tsx:0`
- Code: `className={cn(
                        'flex items-center space-x-3 px-3 py-3 rounded-lg transition-colors btn-touch',
                        isActive
                          ? 'bg-blue-50 text-blue-700 border border-blue-200'
                          : 'text-gray-700 hover:`
- Description: Dynamic class names may cause hydration mismatches
- Fix: Ensure consistent class name generation or use client-side only


**Dynamic class names without guards**
- File: `src/components/layout/MobileLayout.tsx:0`
- Code: `className={cn(
                  'flex items-center space-x-3 px-3 py-2 rounded-lg transition-colors',
                  isActive
                    ? 'bg-blue-50 text-blue-700 border border-blue-200'
                    : 'text-gray-700 hover:`
- Description: Dynamic class names may cause hydration mismatches
- Fix: Ensure consistent class name generation or use client-side only


**Dynamic class names without guards**
- File: `src/components/layout/page-layout.tsx:0`
- Code: `className={`inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 ${
                    action.variant === 'outline'
                      ? 'border border-input bg-background hover:bg-accent hover:text-accent-foreground'
                      : 'bg-primary text-primary-foreground hover:`
- Description: Dynamic class names may cause hydration mismatches
- Fix: Ensure consistent class name generation or use client-side only


**Dynamic class names without guards**
- File: `src/components/layout/stats-cards-grid.tsx:80`
- Code: `className={card.trend.isPositive ? 'text-green-600' :`
- Description: Dynamic class names may cause hydration mismatches
- Fix: Ensure consistent class name generation or use client-side only


**Dynamic class names without guards**
- File: `src/components/mobile/TouchOptimizedComponents.tsx:0`
- Code: `className={cn(
            'absolute inset-y-0 left-0 flex items-center justify-start pl-4 pr-8',
            leftAction.color,
            'transform transition-transform duration-200',
            dragX > 0 ? 'translate-x-0' :`
- Description: Dynamic class names may cause hydration mismatches
- Fix: Ensure consistent class name generation or use client-side only


**Dynamic class names without guards**
- File: `src/components/mobile/TouchOptimizedComponents.tsx:0`
- Code: `className={cn(
            'absolute inset-y-0 right-0 flex items-center justify-end pr-4 pl-8',
            rightAction.color,
            'transform transition-transform duration-200',
            dragX < 0 ? 'translate-x-0' :`
- Description: Dynamic class names may cause hydration mismatches
- Fix: Ensure consistent class name generation or use client-side only


**Dynamic class names without guards**
- File: `src/components/mobile/TouchOptimizedComponents.tsx:0`
- Code: `className={cn(
        'flex items-start space-x-3 p-4 rounded-lg border transition-colors btn-touch text-left w-full',
        checked
          ? 'bg-blue-50 border-blue-200'
          : 'bg-white border-gray-200 hover:`
- Description: Dynamic class names may cause hydration mismatches
- Fix: Ensure consistent class name generation or use client-side only


**Dynamic class names without guards**
- File: `src/components/mobile/TouchOptimizedComponents.tsx:0`
- Code: `className={cn(
          'flex-shrink-0 w-6 h-6 rounded border-2 flex items-center justify-center transition-colors',
          checked
            ? 'bg-blue-600 border-blue-600'
            :`
- Description: Dynamic class names may cause hydration mismatches
- Fix: Ensure consistent class name generation or use client-side only


**Dynamic class names without guards**
- File: `src/components/modals/AppointmentModal.tsx:474`
- Code: `className={hasConflicts ? "bg-orange-600 hover:bg-orange-700" :`
- Description: Dynamic class names may cause hydration mismatches
- Fix: Ensure consistent class name generation or use client-side only


**Dynamic class names without guards**
- File: `src/components/treatment/TreatmentCategoryManager.tsx:0`
- Code: `className={
                            formData.color === color.value 
                              ? color.class 
                              :`
- Description: Dynamic class names may cause hydration mismatches
- Fix: Ensure consistent class name generation or use client-side only


**Dynamic class names without guards**
- File: `src/components/ui/appointment-type-select.tsx:0`
- Code: `className={cn(
                        "mr-2 h-4 w-4",
                        value === type.id ? "opacity-100" :`
- Description: Dynamic class names may cause hydration mismatches
- Fix: Ensure consistent class name generation or use client-side only


**Dynamic class names without guards**
- File: `src/components/ui/bulk-actions.tsx:180`
- Code: `className={action.variant === 'destructive' ? 'text-destructive focus:text-destructive' :`
- Description: Dynamic class names may cause hydration mismatches
- Fix: Ensure consistent class name generation or use client-side only


**Dynamic class names without guards**
- File: `src/components/ui/bulk-actions.tsx:231`
- Code: `className={pendingAction?.variant === 'destructive' ? 'bg-destructive text-destructive-foreground hover:bg-destructive/90' :`
- Description: Dynamic class names may cause hydration mismatches
- Fix: Ensure consistent class name generation or use client-side only


**Dynamic class names without guards**
- File: `src/components/ui/chart.tsx:0`
- Code: `className={cn(
                      'flex flex-1 justify-between leading-none',
                      nestLabel ? 'items-end' :`
- Description: Dynamic class names may cause hydration mismatches
- Fix: Ensure consistent class name generation or use client-side only


**Dynamic class names without guards**
- File: `src/components/ui/chart.tsx:0`
- Code: `className={cn(
        'flex items-center justify-center gap-4',
        verticalAlign === 'top' ? 'pb-3' :`
- Description: Dynamic class names may cause hydration mismatches
- Fix: Ensure consistent class name generation or use client-side only


**Dynamic class names without guards**
- File: `src/components/ui/client-search-select.tsx:0`
- Code: `className={cn(
                            "mr-2 h-4 w-4 text-primary",
                            value === client.id ? "opacity-100" :`
- Description: Dynamic class names may cause hydration mismatches
- Fix: Ensure consistent class name generation or use client-side only


**Dynamic class names without guards**
- File: `src/components/ui/pagination.tsx:0`
- Code: `className={cn(
        buttonVariants({
          variant: isActive ? 'outline' :`
- Description: Dynamic class names may cause hydration mismatches
- Fix: Ensure consistent class name generation or use client-side only


**Dynamic class names without guards**
- File: `src/components/ui/sidebar.tsx:0`
- Code: `className={cn(
          'relative w-(--sidebar-width) bg-transparent transition-[width] duration-200 ease-linear',
          'group-data-[collapsible=offcanvas]:w-0',
          'group-data-[side=right]:rotate-180',
          variant === 'floating' || variant === 'inset'
            ? 'group-data-[collapsible=icon]:w-[calc(var(--sidebar-width-icon)+(--spacing(4)))]'
            : 'group-data-[collapsible=icon]:`
- Description: Dynamic class names may cause hydration mismatches
- Fix: Ensure consistent class name generation or use client-side only


**Dynamic class names without guards**
- File: `src/components/ui/sidebar.tsx:0`
- Code: `className={cn(
          'fixed inset-y-0 z-10 hidden h-svh w-(--sidebar-width) transition-[left,right,width] duration-200 ease-linear md:flex',
          side === 'left'
            ? 'left-0 group-data-[collapsible=offcanvas]:left-[calc(var(--sidebar-width)*-1)]'
            : 'right-0 group-data-[collapsible=offcanvas]:right-[calc(var(--sidebar-width)*-1)]',
          // Adjust the padding for floating and inset variants.
          variant === 'floating' || variant === 'inset'
            ? 'p-2 group-data-[collapsible=icon]:w-[calc(var(--sidebar-width-icon)+(--spacing(4))+2px)]'
            : 'group-data-[collapsible=icon]:w-(--sidebar-width-icon) group-data-[side=left]:border-r group-data-[side=right]:`
- Description: Dynamic class names may cause hydration mismatches
- Fix: Ensure consistent class name generation or use client-side only


**Dynamic class names without guards**
- File: `src/components/ui/table/data-table-faceted-filter.tsx:0`
- Code: `className={cn(
                        'border-primary flex size-4 items-center justify-center rounded-sm border',
                        isSelected
                          ? 'bg-primary'
                          : 'opacity-50 [&_svg]:`
- Description: Dynamic class names may cause hydration mismatches
- Fix: Ensure consistent class name generation or use client-side only


**Dynamic class names without guards**
- File: `src/components/ui/table/data-table-view-options.tsx:0`
- Code: `className={cn(
                      'ml-auto size-4 shrink-0',
                      column.getIsVisible() ? 'opacity-100' :`
- Description: Dynamic class names may cause hydration mismatches
- Fix: Ensure consistent class name generation or use client-side only


**Dynamic class names without guards**
- File: `src/components/ui/table-skeleton.tsx:0`
- Code: `className={cn(
                    "h-4",
                    colIndex === 0 ? "w-24" : // 姓名列稍宽
                    colIndex === 1 ? "w-28" : // 电话列
                    colIndex === 2 ? "w-32" : // 邮箱列
                    colIndex === 3 ? "w-16" : // 状态列
                    colIndex === 4 ? "w-20" :`
- Description: Dynamic class names may cause hydration mismatches
- Fix: Ensure consistent class name generation or use client-side only


**Dynamic class names without guards**
- File: `src/components/ui/treatment-search-select.tsx:0`
- Code: `className={cn(
                          "mr-2 h-4 w-4",
                          value === treatment.id ? "opacity-100" :`
- Description: Dynamic class names may cause hydration mismatches
- Fix: Ensure consistent class name generation or use client-side only


**Dynamic class names without guards**
- File: `src/features/kanban/components/board-column.tsx:0`
- Code: `className={variants({
        dragging: isOverlay ? 'overlay' : isDragging ? 'over' :`
- Description: Dynamic class names may cause hydration mismatches
- Fix: Ensure consistent class name generation or use client-side only


**Dynamic class names without guards**
- File: `src/features/kanban/components/board-column.tsx:0`
- Code: `className={variations({
            dragging: dndContext.active ? 'active' :`
- Description: Dynamic class names may cause hydration mismatches
- Fix: Ensure consistent class name generation or use client-side only


**Dynamic class names without guards**
- File: `src/features/kanban/components/task-card.tsx:0`
- Code: `className={variants({
        dragging: isOverlay ? 'overlay' : isDragging ? 'over' :`
- Description: Dynamic class names may cause hydration mismatches
- Fix: Ensure consistent class name generation or use client-side only


**Dynamic class names without guards**
- File: `src/features/profile/components/profile-create-form.tsx:0`
- Code: `className={cn(
              currentStep === 1
                ? 'w-full md:inline-block'
                : 'gap-8 md:grid md:`
- Description: Dynamic class names may cause hydration mismatches
- Fix: Ensure consistent class name generation or use client-side only


**toLocaleDateString without options**
- File: `src/lib/data-export.ts:38`
- Code: `.toLocaleDateString('zh-CN')`
- Description: toLocaleDateString may render differently due to timezone differences
- Fix: Use consistent date formatting with explicit options


**toLocaleDateString without options**
- File: `src/lib/data-export.ts:38`
- Code: `.toLocaleDateString('zh-CN')`
- Description: toLocaleDateString may render differently due to timezone differences
- Fix: Use consistent date formatting with explicit options


## Issues by Type


### Math.random() usage (26 occurrences)

- `src/app/api/admin/performance/route.ts:107` - `Math.random()`
- `src/app/api/admin/performance/route.ts:107` - `Math.random()`
- `src/app/api/admin/performance/route.ts:107` - `Math.random()`
- `src/app/api/admin/performance/route.ts:107` - `Math.random()`
- `src/app/api/admin/performance/route.ts:107` - `Math.random()`
- `src/app/api/admin/performance/route.ts:107` - `Math.random()`
- `src/app/api/admin/performance/route.ts:107` - `Math.random()`
- `src/components/analytics/BusinessAnalyticsDashboard.tsx:127` - `Math.random()`
- `src/components/analytics/BusinessAnalyticsDashboard.tsx:127` - `Math.random()`
- `src/components/analytics/BusinessAnalyticsDashboard.tsx:127` - `Math.random()`
- `src/components/finance/FinancialReports.tsx:122` - `Math.random()`
- `src/components/finance/FinancialReports.tsx:122` - `Math.random()`
- `src/components/finance/FinancialReports.tsx:122` - `Math.random()`
- `src/components/ui/sidebar.tsx:610` - `Math.random()`
- `src/features/overview/components/bar-graph-skeleton.tsx:32` - `Math.random()`
- `src/lib/auth/rbac.ts:266` - `Math.random()`
- `src/lib/auth/rbac.ts:266` - `Math.random()`
- `src/lib/compliance/automation.ts:270` - `Math.random()`
- `src/lib/integrations/payment-gateways.ts:293` - `Math.random()`
- `src/lib/integrations/payment-gateways.ts:293` - `Math.random()`
- `src/lib/integrations/payment-gateways.ts:293` - `Math.random()`
- `src/lib/integrations/payment-gateways.ts:293` - `Math.random()`
- `src/lib/integrations/payment-gateways.ts:293` - `Math.random()`
- `src/lib/integrations/payment-gateways.ts:293` - `Math.random()`
- `src/lib/security/monitoring.ts:255` - `Math.random()`
- `src/lib/security/monitoring.ts:255` - `Math.random()`


### Date.now() usage (88 occurrences)

- `src/app/api/admin/performance/route.ts:17` - `Date.now()`
- `src/app/api/admin/performance/route.ts:17` - `Date.now()`
- `src/app/api/health/db/route.ts:25` - `Date.now()`
- `src/app/api/health/db/route.ts:25` - `Date.now()`
- `src/app/api/health/db/route.ts:25` - `Date.now()`
- `src/app/api/health/route.ts:34` - `Date.now()`
- `src/app/api/health/route.ts:34` - `Date.now()`
- `src/app/api/health/route.ts:34` - `Date.now()`
- `src/app/api/health/route.ts:34` - `Date.now()`
- `src/app/api/health/route.ts:34` - `Date.now()`
- `src/app/api/invoices/route.ts:56` - `Date.now()`
- `src/app/dashboard/settings/appointment-types/page.tsx:146` - `Date.now()`
- `src/app/dashboard/settings/treatment-categories/page.tsx:147` - `Date.now()`
- `src/components/admin/SecurityDashboard.tsx:163` - `Date.now()`
- `src/components/admin/SecurityDashboard.tsx:163` - `Date.now()`
- `src/components/client/ClientTagManager.tsx:108` - `Date.now()`
- `src/components/dashboard/CRMSummaryCard.tsx:66` - `Date.now()`
- `src/components/dashboard/CRMSummaryCard.tsx:66` - `Date.now()`
- `src/components/finance/PaymentProcessor.tsx:125` - `Date.now()`
- `src/lib/ai/intelligent-automation.ts:544` - `Date.now()`
- `src/lib/ai/intelligent-automation.ts:544` - `Date.now()`
- `src/lib/ai/intelligent-automation.ts:544` - `Date.now()`
- `src/lib/analytics/business-intelligence.ts:83` - `Date.now()`
- `src/lib/analytics/business-intelligence.ts:83` - `Date.now()`
- `src/lib/analytics/business-intelligence.ts:83` - `Date.now()`
- `src/lib/auth/rbac.ts:266` - `Date.now()`
- `src/lib/auth/rbac.ts:266` - `Date.now()`
- `src/lib/auth/rbac.ts:266` - `Date.now()`
- `src/lib/auth/rbac.ts:266` - `Date.now()`
- `src/lib/cache.ts:84` - `Date.now()`
- `src/lib/cache.ts:84` - `Date.now()`
- `src/lib/cache.ts:84` - `Date.now()`
- `src/lib/cache.ts:84` - `Date.now()`
- `src/lib/cache.ts:84` - `Date.now()`
- `src/lib/compliance/automation.ts:270` - `Date.now()`
- `src/lib/compliance/automation.ts:270` - `Date.now()`
- `src/lib/compliance/automation.ts:270` - `Date.now()`
- `src/lib/compliance/automation.ts:270` - `Date.now()`
- `src/lib/compliance/hipaa.ts:506` - `Date.now()`
- `src/lib/database/performance.ts:138` - `Date.now()`
- `src/lib/database/performance.ts:138` - `Date.now()`
- `src/lib/database/performance.ts:138` - `Date.now()`
- `src/lib/database/performance.ts:138` - `Date.now()`
- `src/lib/database/performance.ts:138` - `Date.now()`
- `src/lib/database/performance.ts:138` - `Date.now()`
- `src/lib/integrations/payment-gateways.ts:293` - `Date.now()`
- `src/lib/integrations/payment-gateways.ts:293` - `Date.now()`
- `src/lib/integrations/payment-gateways.ts:293` - `Date.now()`
- `src/lib/integrations/payment-gateways.ts:293` - `Date.now()`
- `src/lib/integrations/payment-gateways.ts:293` - `Date.now()`
- `src/lib/integrations/payment-gateways.ts:293` - `Date.now()`
- `src/lib/integrations/payment-gateways.ts:293` - `Date.now()`
- `src/lib/integrations/payment-gateways.ts:293` - `Date.now()`
- `src/lib/integrations/payment-gateways.ts:293` - `Date.now()`
- `src/lib/integrations/payment-gateways.ts:293` - `Date.now()`
- `src/lib/integrations/payment-gateways.ts:293` - `Date.now()`
- `src/lib/integrations/payment-gateways.ts:293` - `Date.now()`
- `src/lib/integrations/payment-gateways.ts:293` - `Date.now()`
- `src/lib/integrations/payment-gateways.ts:293` - `Date.now()`
- `src/lib/integrations/payment-gateways.ts:293` - `Date.now()`
- `src/lib/integrations/payment-gateways.ts:293` - `Date.now()`
- `src/lib/integrations/payment-gateways.ts:293` - `Date.now()`
- `src/lib/integrations/payment-gateways.ts:293` - `Date.now()`
- `src/lib/logger.ts:297` - `Date.now()`
- `src/lib/logger.ts:297` - `Date.now()`
- `src/lib/logger.ts:297` - `Date.now()`
- `src/lib/monitoring.ts:70` - `Date.now()`
- `src/lib/monitoring.ts:70` - `Date.now()`
- `src/lib/monitoring.ts:70` - `Date.now()`
- `src/lib/monitoring.ts:70` - `Date.now()`
- `src/lib/monitoring.ts:70` - `Date.now()`
- `src/lib/monitoring.ts:70` - `Date.now()`
- `src/lib/monitoring.ts:70` - `Date.now()`
- `src/lib/monitoring.ts:70` - `Date.now()`
- `src/lib/monitoring.ts:70` - `Date.now()`
- `src/lib/monitoring.ts:70` - `Date.now()`
- `src/lib/react-query.ts:167` - `Date.now()`
- `src/lib/react-query.ts:167` - `Date.now()`
- `src/lib/react-query.ts:167` - `Date.now()`
- `src/lib/security/monitoring.ts:255` - `Date.now()`
- `src/lib/security/monitoring.ts:255` - `Date.now()`
- `src/lib/security/monitoring.ts:255` - `Date.now()`
- `src/lib/security.ts:128` - `Date.now()`
- `src/lib/security.ts:128` - `Date.now()`
- `src/lib/security.ts:128` - `Date.now()`
- `src/lib/supabase/queries.ts:610` - `Date.now()`
- `src/lib/supabase/queries.ts:610` - `Date.now()`
- `src/lib/supabase/queries.ts:610` - `Date.now()`


### new Date() without parameters (61 occurrences)

- `src/app/dashboard/calendar/page.tsx:52` - `new Date()`
- `src/app/dashboard/clients/page.tsx:194` - `new Date()`
- `src/components/admin/PerformanceDashboard.tsx:74` - `new Date()`
- `src/components/admin/PerformanceDashboard.tsx:74` - `new Date()`
- `src/components/admin/SecurityDashboard.tsx:90` - `new Date()`
- `src/components/admin/SecurityDashboard.tsx:90` - `new Date()`
- `src/components/analytics/AdvancedAnalyticsDashboard.tsx:69` - `new Date()`
- `src/components/analytics/AdvancedAnalyticsDashboard.tsx:69` - `new Date()`
- `src/components/analytics/AdvancedAnalyticsDashboard.tsx:69` - `new Date()`
- `src/components/analytics/BusinessAnalyticsDashboard.tsx:126` - `new Date()`
- `src/components/appointment/AppointmentReminders.tsx:45` - `new Date()`
- `src/components/finance/FinancialReports.tsx:87` - `new Date()`
- `src/components/finance/PaymentProcessor.tsx:129` - `new Date()`
- `src/components/modals/AppointmentModal.tsx:125` - `new Date()`
- `src/components/modals/AppointmentModal.tsx:125` - `new Date()`
- `src/components/modals/AppointmentModal.tsx:125` - `new Date()`
- `src/components/modals/InvoiceModal.tsx:117` - `new Date()`
- `src/components/modals/InvoiceModal.tsx:117` - `new Date()`
- `src/lib/ai/intelligent-automation.ts:363` - `new Date()`
- `src/lib/analytics/business-intelligence.ts:113` - `new Date()`
- `src/lib/auth/rbac.ts:250` - `new Date()`
- `src/lib/auth/rbac.ts:250` - `new Date()`
- `src/lib/auth/rbac.ts:250` - `new Date()`
- `src/lib/auth/rbac.ts:250` - `new Date()`
- `src/lib/auth/rbac.ts:250` - `new Date()`
- `src/lib/auth/rbac.ts:250` - `new Date()`
- `src/lib/auth/rbac.ts:250` - `new Date()`
- `src/lib/auth/rbac.ts:250` - `new Date()`
- `src/lib/auth/rbac.ts:250` - `new Date()`
- `src/lib/auth/rbac.ts:250` - `new Date()`
- `src/lib/auth/rbac.ts:250` - `new Date()`
- `src/lib/compliance/automation.ts:170` - `new Date()`
- `src/lib/compliance/automation.ts:170` - `new Date()`
- `src/lib/compliance/automation.ts:170` - `new Date()`
- `src/lib/compliance/automation.ts:170` - `new Date()`
- `src/lib/compliance/automation.ts:170` - `new Date()`
- `src/lib/compliance/hipaa.ts:301` - `new Date()`
- `src/lib/integrations/payment-gateways.ts:268` - `new Date()`
- `src/lib/integrations/payment-gateways.ts:268` - `new Date()`
- `src/lib/integrations/payment-gateways.ts:268` - `new Date()`
- `src/lib/integrations/payment-gateways.ts:268` - `new Date()`
- `src/lib/integrations/payment-gateways.ts:268` - `new Date()`
- `src/lib/integrations/payment-gateways.ts:268` - `new Date()`
- `src/lib/integrations/payment-gateways.ts:268` - `new Date()`
- `src/lib/integrations/payment-gateways.ts:268` - `new Date()`
- `src/lib/integrations/payment-gateways.ts:268` - `new Date()`
- `src/lib/integrations/payment-gateways.ts:268` - `new Date()`
- `src/lib/integrations/payment-gateways.ts:268` - `new Date()`
- `src/lib/integrations/payment-gateways.ts:268` - `new Date()`
- `src/lib/integrations/payment-gateways.ts:268` - `new Date()`
- `src/lib/integrations/payment-gateways.ts:268` - `new Date()`
- `src/lib/integrations/payment-gateways.ts:268` - `new Date()`
- `src/lib/integrations/payment-gateways.ts:268` - `new Date()`
- `src/lib/integrations/payment-gateways.ts:268` - `new Date()`
- `src/lib/integrations/payment-gateways.ts:268` - `new Date()`
- `src/lib/integrations/payment-gateways.ts:268` - `new Date()`
- `src/lib/security/monitoring.ts:258` - `new Date()`
- `src/lib/security/monitoring.ts:258` - `new Date()`
- `src/lib/security/monitoring.ts:258` - `new Date()`
- `src/lib/security/monitoring.ts:258` - `new Date()`
- `src/lib/supabase/queries.ts:155` - `new Date()`


### toLocaleDateString without options (7 occurrences)

- `src/app/dashboard/invoices/page.tsx:144` - `.toLocaleDateString('zh-CN')`
- `src/app/dashboard/payments/page.tsx:150` - `.toLocaleDateString('zh-CN')`
- `src/app/dashboard/settings/appointment-types/page.tsx:327` - `.toLocaleDateString('zh-CN')`
- `src/app/dashboard/settings/treatment-categories/page.tsx:304` - `.toLocaleDateString('zh-CN')`
- `src/components/dashboard/CRMSummaryCard.tsx:275` - `.toLocaleDateString('zh-CN')`
- `src/lib/data-export.ts:38` - `.toLocaleDateString('zh-CN')`
- `src/lib/data-export.ts:38` - `.toLocaleDateString('zh-CN')`


### window object usage (28 occurrences)

- `src/app/dashboard/product/page.tsx:39` - `window.`
- `src/app/layout.tsx:43` - `window.`
- `src/components/active-theme.tsx:17` - `window.`
- `src/components/admin/SecurityDashboard.tsx:230` - `window.`
- `src/components/admin/SecurityDashboard.tsx:230` - `window.`
- `src/components/dashboard/QuickActionsToolbar.tsx:31` - `window.`
- `src/components/finance/PaymentProcessor.tsx:161` - `window.`
- `src/components/layout/MobileLayout.tsx:106` - `window.`
- `src/components/layout/MobileLayout.tsx:106` - `window.`
- `src/components/layout/MobileLayout.tsx:106` - `window.`
- `src/components/mobile/TouchOptimizedComponents.tsx:275` - `window.`
- `src/components/modals/AppointmentModal.tsx:201` - `window.`
- `src/components/ui/responsive-layout.tsx:28` - `window.`
- `src/components/ui/responsive-layout.tsx:28` - `window.`
- `src/components/ui/responsive-layout.tsx:28` - `window.`
- `src/components/ui/sidebar.tsx:107` - `window.`
- `src/components/ui/sidebar.tsx:107` - `window.`
- `src/hooks/use-debounced-callback.ts:12` - `window.`
- `src/hooks/use-debounced-callback.ts:12` - `window.`
- `src/hooks/use-debounced-callback.ts:12` - `window.`
- `src/hooks/use-media-query.ts:7` - `window.`
- `src/hooks/use-mobile.tsx:11` - `window.`
- `src/hooks/use-mobile.tsx:11` - `window.`
- `src/hooks/use-mobile.tsx:11` - `window.`
- `src/lib/google-maps.ts:40` - `window.`
- `src/lib/google-maps.ts:40` - `window.`
- `src/lib/google-maps.ts:40` - `window.`
- `src/lib/google-maps.ts:40` - `window.`


### localStorage usage (13 occurrences)

- `src/app/dashboard/settings/appointment-types/page.tsx:86` - `localStorage.`
- `src/app/dashboard/settings/appointment-types/page.tsx:86` - `localStorage.`
- `src/app/dashboard/settings/treatment-categories/page.tsx:91` - `localStorage.`
- `src/app/dashboard/settings/treatment-categories/page.tsx:91` - `localStorage.`
- `src/app/dashboard/settings/treatment-categories/page.tsx:91` - `localStorage.`
- `src/app/layout.tsx:43` - `localStorage.`
- `src/app/layout.tsx:43` - `localStorage.`
- `src/components/ui/appointment-type-select.tsx:57` - `localStorage.`
- `src/components/ui/appointment-type-select.tsx:57` - `localStorage.`
- `src/components/ui/global-search.tsx:67` - `localStorage.`
- `src/components/ui/global-search.tsx:67` - `localStorage.`
- `src/lib/working-hours.ts:30` - `localStorage.`
- `src/lib/working-hours.ts:30` - `localStorage.`


### document object usage (28 occurrences)

- `src/app/dashboard/settings/page.tsx:202` - `document.`
- `src/app/dashboard/settings/page.tsx:202` - `document.`
- `src/app/layout.tsx:44` - `document.`
- `src/components/active-theme.tsx:17` - `document.`
- `src/components/active-theme.tsx:17` - `document.`
- `src/components/active-theme.tsx:17` - `document.`
- `src/components/active-theme.tsx:17` - `document.`
- `src/components/active-theme.tsx:17` - `document.`
- `src/components/admin/SecurityDashboard.tsx:231` - `document.`
- `src/components/admin/SecurityDashboard.tsx:231` - `document.`
- `src/components/admin/SecurityDashboard.tsx:231` - `document.`
- `src/components/layout/MobileLayout.tsx:122` - `document.`
- `src/components/layout/MobileLayout.tsx:122` - `document.`
- `src/components/layout/MobileLayout.tsx:122` - `document.`
- `src/components/layout/ThemeToggle/theme-toggle.tsx:15` - `document.`
- `src/components/layout/ThemeToggle/theme-toggle.tsx:15` - `document.`
- `src/components/layout/ThemeToggle/theme-toggle.tsx:15` - `document.`
- `src/components/ui/global-search.tsx:61` - `document.`
- `src/components/ui/global-search.tsx:61` - `document.`
- `src/components/ui/sidebar.tsx:85` - `document.`
- `src/features/kanban/components/column-action.tsx:103` - `document.`
- `src/features/kanban/components/kanban-board.tsx:222` - `document.`
- `src/lib/data-export.ts:70` - `document.`
- `src/lib/data-export.ts:70` - `document.`
- `src/lib/data-export.ts:70` - `document.`
- `src/lib/google-maps.ts:51` - `document.`
- `src/lib/google-maps.ts:51` - `document.`
- `src/lib/google-maps.ts:51` - `document.`


### Dynamic class names without guards (28 occurrences)

- `src/components/client/ClientPreferences.tsx:0` - `className={
                      formData.skin_sensitivity_level === level.value 
                        ? level.color 
                        :`
- `src/components/layout/MobileLayout.tsx:0` - `className={cn(
                        'flex items-center space-x-3 px-3 py-3 rounded-lg transition-colors btn-touch',
                        isActive
                          ? 'bg-blue-50 text-blue-700 border border-blue-200'
                          : 'text-gray-700 hover:`
- `src/components/layout/MobileLayout.tsx:0` - `className={cn(
                  'flex items-center space-x-3 px-3 py-2 rounded-lg transition-colors',
                  isActive
                    ? 'bg-blue-50 text-blue-700 border border-blue-200'
                    : 'text-gray-700 hover:`
- `src/components/layout/page-layout.tsx:0` - `className={`inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 ${
                    action.variant === 'outline'
                      ? 'border border-input bg-background hover:bg-accent hover:text-accent-foreground'
                      : 'bg-primary text-primary-foreground hover:`
- `src/components/layout/stats-cards-grid.tsx:80` - `className={card.trend.isPositive ? 'text-green-600' :`
- `src/components/mobile/TouchOptimizedComponents.tsx:0` - `className={cn(
            'absolute inset-y-0 left-0 flex items-center justify-start pl-4 pr-8',
            leftAction.color,
            'transform transition-transform duration-200',
            dragX > 0 ? 'translate-x-0' :`
- `src/components/mobile/TouchOptimizedComponents.tsx:0` - `className={cn(
            'absolute inset-y-0 right-0 flex items-center justify-end pr-4 pl-8',
            rightAction.color,
            'transform transition-transform duration-200',
            dragX < 0 ? 'translate-x-0' :`
- `src/components/mobile/TouchOptimizedComponents.tsx:0` - `className={cn(
        'flex items-start space-x-3 p-4 rounded-lg border transition-colors btn-touch text-left w-full',
        checked
          ? 'bg-blue-50 border-blue-200'
          : 'bg-white border-gray-200 hover:`
- `src/components/mobile/TouchOptimizedComponents.tsx:0` - `className={cn(
          'flex-shrink-0 w-6 h-6 rounded border-2 flex items-center justify-center transition-colors',
          checked
            ? 'bg-blue-600 border-blue-600'
            :`
- `src/components/modals/AppointmentModal.tsx:474` - `className={hasConflicts ? "bg-orange-600 hover:bg-orange-700" :`
- `src/components/treatment/TreatmentCategoryManager.tsx:0` - `className={
                            formData.color === color.value 
                              ? color.class 
                              :`
- `src/components/ui/appointment-type-select.tsx:0` - `className={cn(
                        "mr-2 h-4 w-4",
                        value === type.id ? "opacity-100" :`
- `src/components/ui/bulk-actions.tsx:180` - `className={action.variant === 'destructive' ? 'text-destructive focus:text-destructive' :`
- `src/components/ui/bulk-actions.tsx:231` - `className={pendingAction?.variant === 'destructive' ? 'bg-destructive text-destructive-foreground hover:bg-destructive/90' :`
- `src/components/ui/chart.tsx:0` - `className={cn(
                      'flex flex-1 justify-between leading-none',
                      nestLabel ? 'items-end' :`
- `src/components/ui/chart.tsx:0` - `className={cn(
        'flex items-center justify-center gap-4',
        verticalAlign === 'top' ? 'pb-3' :`
- `src/components/ui/client-search-select.tsx:0` - `className={cn(
                            "mr-2 h-4 w-4 text-primary",
                            value === client.id ? "opacity-100" :`
- `src/components/ui/pagination.tsx:0` - `className={cn(
        buttonVariants({
          variant: isActive ? 'outline' :`
- `src/components/ui/sidebar.tsx:0` - `className={cn(
          'relative w-(--sidebar-width) bg-transparent transition-[width] duration-200 ease-linear',
          'group-data-[collapsible=offcanvas]:w-0',
          'group-data-[side=right]:rotate-180',
          variant === 'floating' || variant === 'inset'
            ? 'group-data-[collapsible=icon]:w-[calc(var(--sidebar-width-icon)+(--spacing(4)))]'
            : 'group-data-[collapsible=icon]:`
- `src/components/ui/sidebar.tsx:0` - `className={cn(
          'fixed inset-y-0 z-10 hidden h-svh w-(--sidebar-width) transition-[left,right,width] duration-200 ease-linear md:flex',
          side === 'left'
            ? 'left-0 group-data-[collapsible=offcanvas]:left-[calc(var(--sidebar-width)*-1)]'
            : 'right-0 group-data-[collapsible=offcanvas]:right-[calc(var(--sidebar-width)*-1)]',
          // Adjust the padding for floating and inset variants.
          variant === 'floating' || variant === 'inset'
            ? 'p-2 group-data-[collapsible=icon]:w-[calc(var(--sidebar-width-icon)+(--spacing(4))+2px)]'
            : 'group-data-[collapsible=icon]:w-(--sidebar-width-icon) group-data-[side=left]:border-r group-data-[side=right]:`
- `src/components/ui/table/data-table-faceted-filter.tsx:0` - `className={cn(
                        'border-primary flex size-4 items-center justify-center rounded-sm border',
                        isSelected
                          ? 'bg-primary'
                          : 'opacity-50 [&_svg]:`
- `src/components/ui/table/data-table-view-options.tsx:0` - `className={cn(
                      'ml-auto size-4 shrink-0',
                      column.getIsVisible() ? 'opacity-100' :`
- `src/components/ui/table-skeleton.tsx:0` - `className={cn(
                    "h-4",
                    colIndex === 0 ? "w-24" : // 姓名列稍宽
                    colIndex === 1 ? "w-28" : // 电话列
                    colIndex === 2 ? "w-32" : // 邮箱列
                    colIndex === 3 ? "w-16" : // 状态列
                    colIndex === 4 ? "w-20" :`
- `src/components/ui/treatment-search-select.tsx:0` - `className={cn(
                          "mr-2 h-4 w-4",
                          value === treatment.id ? "opacity-100" :`
- `src/features/kanban/components/board-column.tsx:0` - `className={variants({
        dragging: isOverlay ? 'overlay' : isDragging ? 'over' :`
- `src/features/kanban/components/board-column.tsx:0` - `className={variations({
            dragging: dndContext.active ? 'active' :`
- `src/features/kanban/components/task-card.tsx:0` - `className={variants({
        dragging: isOverlay ? 'overlay' : isDragging ? 'over' :`
- `src/features/profile/components/profile-create-form.tsx:0` - `className={cn(
              currentStep === 1
                ? 'w-full md:inline-block'
                : 'gap-8 md:grid md:`


## Recommended Fixes

### 1. Add Hydration Guards

For client-side only code, use the mounted pattern:

```tsx
const [mounted, setMounted] = useState(false)

useEffect(() => {
  setMounted(true)
}, [])

if (!mounted) return null // or loading state

// Client-side only code here
```

### 2. Fix Date Handling

Replace inconsistent date formatting:

```tsx
// ❌ Bad - may cause hydration mismatch
const date = new Date().toLocaleDateString('zh-CN')

// ✅ Good - consistent formatting
const formatDate = (dateString: string) => {
  if (!mounted) return ''
  return new Date(dateString).toLocaleDateString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit'
  })
}
```

### 3. Replace Random Values

Replace Math.random() with deterministic calculations:

```tsx
// ❌ Bad - different values on server vs client
const growth = Math.round(Math.random() * 20)

// ✅ Good - deterministic calculation
const growth = Math.min(Math.max(Math.floor((data.length * 0.1) + 5), 0), 50)
```

### 4. Guard Browser APIs

Add proper checks for browser-only APIs:

```tsx
// ❌ Bad - will fail during SSR
const value = localStorage.getItem('key')

// ✅ Good - properly guarded
const value = typeof window !== 'undefined' ? localStorage.getItem('key') : null
```

## Production Readiness

❌ **NOT READY** - Critical hydration issues must be fixed before production


⚠️ **Action Required**: 279 hydration issues found. Fix high severity issues before production deployment.

---

*Run `pnpm fix:hydration` to re-scan after making fixes.*
