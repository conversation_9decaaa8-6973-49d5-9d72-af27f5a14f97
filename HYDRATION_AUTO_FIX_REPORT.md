# Hydration Auto-Fix Report

Generated on: 2025-07-22T17:17:21.713Z

## Summary

- **Files Processed**: 234
- **Total Fixes Applied**: 35

## Auto-Fix Patterns Applied


### Window object usage
- **Description**: Add typeof window check
- **Pattern**: `(?<!typeof\s+)window\.`


### Document object usage
- **Description**: Add typeof document check
- **Pattern**: `(?<!typeof\s+)document\.`


### LocalStorage usage
- **Description**: Add typeof window check for localStorage
- **Pattern**: `localStorage\.`


### SessionStorage usage
- **Description**: Add typeof window check for sessionStorage
- **Pattern**: `sessionStorage\.`


### toLocaleDateString without options
- **Description**: Add explicit options to toLocaleDateString
- **Pattern**: `\.toLocaleDateString\(\s*['"]zh-CN['"]\s*\)`


## Manual Fixes Still Needed

The following issues require manual intervention:

### 1. Math.random() Usage
Replace with deterministic calculations:
```tsx
// ❌ Bad
const growth = Math.round(Math.random() * 20)

// ✅ Good
const growth = Math.min(Math.max(Math.floor((data.length * 0.1) + 5), 0), 50)
```

### 2. new Date() Without Parameters
Use static dates or client-side only calculations:
```tsx
// ❌ Bad
const now = new Date()

// ✅ Good - move to useEffect
useEffect(() => {
  const now = new Date()
  // Use now here
}, [])
```

### 3. Dynamic Class Names
Ensure consistent class name generation:
```tsx
// ❌ Bad
className={condition ? 'class-a' : 'class-b'}

// ✅ Good - use hydration guard
const mounted = useHydrationGuard()
className={mounted && condition ? 'class-a' : 'class-b'}
```

## Next Steps

1. Run `pnpm build` to test for remaining hydration issues
2. Check browser console for hydration warnings
3. Use the hydration guard hook for client-side only code
4. Test on actual devices to ensure smooth hydration

---

*Run `pnpm auto-fix:hydration` to apply these fixes again after making changes.*
