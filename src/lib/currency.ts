// 全局货币配置
export const CURRENCY_CONFIG = {
  symbol: '$',
  code: 'USD',
  name: '美元',
  locale: 'en-US'
}

// 格式化货币显示
export function formatCurrency(amount: number | string, options?: {
  showSymbol?: boolean
  precision?: number
}): string {
  const { showSymbol = true, precision = 0 } = options || {}
  
  const numAmount = typeof amount === 'string' ? parseFloat(amount) : amount
  
  if (isNaN(numAmount)) {
    return showSymbol ? `${CURRENCY_CONFIG.symbol}0` : '0'
  }
  
  const formatted = numAmount.toLocaleString(CURRENCY_CONFIG.locale, {
    minimumFractionDigits: precision,
    maximumFractionDigits: precision
  })
  
  return showSymbol ? `${CURRENCY_CONFIG.symbol}${formatted}` : formatted
}

// 解析货币字符串为数字
export function parseCurrency(currencyString: string): number {
  if (!currencyString) return 0
  
  // 移除货币符号和空格
  const cleanString = currencyString
    .replace(CURRENCY_CONFIG.symbol, '')
    .replace(/\s/g, '')
    .replace(/,/g, '')
  
  const parsed = parseFloat(cleanString)
  return isNaN(parsed) ? 0 : parsed
}

// 验证货币输入
export function validateCurrencyInput(input: string): boolean {
  if (!input.trim()) return true // 空值允许
  
  const cleaned = input
    .replace(CURRENCY_CONFIG.symbol, '')
    .replace(/\s/g, '')
    .replace(/,/g, '')
  
  // 检查是否为有效数字格式
  const numberRegex = /^\d+(\.\d{0,2})?$/
  return numberRegex.test(cleaned)
}

// 货币输入组件的格式化函数
export function formatCurrencyInput(value: string): string {
  const cleaned = value.replace(/[^\d.]/g, '')
  const parts = cleaned.split('.')
  
  if (parts.length > 2) {
    // 只保留第一个小数点
    return `${parts[0]  }.${  parts.slice(1).join('')}`
  }
  
  if (parts[1] && parts[1].length > 2) {
    // 限制小数点后两位
    return `${parts[0]  }.${  parts[1].substring(0, 2)}`
  }
  
  return cleaned
}
