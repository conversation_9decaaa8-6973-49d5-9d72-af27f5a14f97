'use client'

import { useState, useEffect } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Badge } from '@/components/ui/badge'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import { 
  CreditCard, 
  DollarSign, 
  Receipt, 
  Calendar,
  CheckCircle,
  AlertCircle,
  Printer,
  Send
} from 'lucide-react'
import { format } from 'date-fns'
import { zhCN } from 'date-fns/locale'
import { showSuccessToast, showErrorToast } from '@/lib/toast-utils'

interface Invoice {
  id: string
  invoice_number: string
  client_id: string
  client_name: string
  treatment_date: string
  total_amount: number
  deposit_amount: number
  remaining_balance: number
  status: string
  due_date: string | null
  items: InvoiceItem[]
}

interface InvoiceItem {
  id: string
  treatment_name: string
  quantity: number
  unit_price: number
  total_price: number
}

interface Payment {
  id: string
  invoice_id: string
  amount: number
  payment_method: string
  payment_date: string
  reference_number?: string
  notes?: string
  processed_by: string
}

interface PaymentProcessorProps {
  invoice: Invoice
  onPaymentSuccess: (payment: Payment) => void
  onClose: () => void
}

const paymentMethods = [
  { value: 'cash', label: '现金', icon: DollarSign },
  { value: 'card', label: '银行卡', icon: CreditCard },
  { value: 'alipay', label: '支付宝', icon: CreditCard },
  { value: 'wechat', label: '微信支付', icon: CreditCard },
  { value: 'transfer', label: '银行转账', icon: CreditCard }
]

export default function PaymentProcessor({ invoice, onPaymentSuccess, onClose }: PaymentProcessorProps) {
  const [paymentData, setPaymentData] = useState({
    amount: invoice.remaining_balance,
    payment_method: 'cash',
    reference_number: '',
    notes: ''
  })
  const [processing, setProcessing] = useState(false)
  const [showReceipt, setShowReceipt] = useState(false)
  const [processedPayment, setProcessedPayment] = useState<Payment | null>(null)

  const handlePaymentSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (paymentData.amount <= 0) {
      showErrorToast('支付金额无效', '支付金额必须大于0')
      return
    }

    if (paymentData.amount > invoice.remaining_balance) {
      showErrorToast('支付金额超出', '支付金额不能超过剩余应付金额')
      return
    }

    try {
      setProcessing(true)
      
      // Here you would process the payment through your API
      // const response = await fetch('/api/payments', {
      //   method: 'POST',
      //   headers: { 'Content-Type': 'application/json' },
      //   body: JSON.stringify({
      //     invoice_id: invoice.id,
      //     ...paymentData
      //   })
      // })
      
      // Mock payment processing
      const newPayment: Payment = {
        id: `payment-${Date.now()}`,
        invoice_id: invoice.id,
        amount: paymentData.amount,
        payment_method: paymentData.payment_method,
        payment_date: new Date().toISOString(),
        reference_number: paymentData.reference_number || undefined,
        notes: paymentData.notes || undefined,
        processed_by: 'current_user' // This would come from auth context
      }

      setProcessedPayment(newPayment)
      setShowReceipt(true)
      
      showSuccessToast(
        '支付处理成功', 
        `已收到 ¥${paymentData.amount} 的${getPaymentMethodLabel(paymentData.payment_method)}支付`
      )
      
      onPaymentSuccess(newPayment)
    } catch (error: any) {
      console.error('Error processing payment:', error)
      showErrorToast('支付处理失败', error.message)
    } finally {
      setProcessing(false)
    }
  }

  const getPaymentMethodLabel = (method: string) => {
    return paymentMethods.find(m => m.value === method)?.label || method
  }

  const getPaymentMethodIcon = (method: string) => {
    return paymentMethods.find(m => m.value === method)?.icon || CreditCard
  }

  const printReceipt = () => {
    typeof window !== "undefined" && window.print()
  }

  const sendReceiptEmail = async () => {
    try {
      // Here you would send receipt via email
      showSuccessToast('收据已发送', '收据已通过邮件发送给客户')
    } catch (error) {
      showErrorToast('发送失败', '无法发送收据邮件')
    }
  }

  if (showReceipt && processedPayment) {
    return (
      <Dialog open={true} onOpenChange={() => setShowReceipt(false)}>
        <DialogContent className="sm:max-w-[500px]">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <CheckCircle className="h-5 w-5 text-green-600" />
              支付成功
            </DialogTitle>
            <DialogDescription>
              支付已成功处理，以下是收据详情
            </DialogDescription>
          </DialogHeader>
          
          <div className="space-y-4 print:space-y-2">
            {/* Receipt Header */}
            <div className="text-center border-b pb-4 print:pb-2">
              <h3 className="text-lg font-bold">美丽诊所</h3>
              <p className="text-sm text-muted-foreground">支付收据</p>
              <p className="text-xs text-muted-foreground">
                {format(new Date(), 'yyyy年MM月dd日 HH:mm', { locale: zhCN })}
              </p>
            </div>

            {/* Payment Details */}
            <div className="space-y-3 print:space-y-1">
              <div className="grid grid-cols-2 gap-4 text-sm">
                <div>
                  <p className="text-muted-foreground">收据编号</p>
                  <p className="font-medium">{processedPayment.id}</p>
                </div>
                <div>
                  <p className="text-muted-foreground">账单编号</p>
                  <p className="font-medium">{invoice.invoice_number}</p>
                </div>
              </div>

              <div className="grid grid-cols-2 gap-4 text-sm">
                <div>
                  <p className="text-muted-foreground">客户姓名</p>
                  <p className="font-medium">{invoice.client_name}</p>
                </div>
                <div>
                  <p className="text-muted-foreground">支付方式</p>
                  <div className="flex items-center gap-1">
                    {(() => {
                      const IconComponent = getPaymentMethodIcon(processedPayment.payment_method)
                      return <IconComponent className="h-4 w-4" />
                    })()}
                    <span className="font-medium">
                      {getPaymentMethodLabel(processedPayment.payment_method)}
                    </span>
                  </div>
                </div>
              </div>

              {processedPayment.reference_number && (
                <div>
                  <p className="text-muted-foreground text-sm">参考号码</p>
                  <p className="font-medium">{processedPayment.reference_number}</p>
                </div>
              )}

              <div className="border-t pt-3 print:pt-1">
                <div className="flex justify-between items-center">
                  <span className="text-lg font-medium">支付金额</span>
                  <span className="text-2xl font-bold text-green-600">
                    ¥{processedPayment.amount.toLocaleString()}
                  </span>
                </div>
              </div>

              <div className="text-sm text-muted-foreground">
                <p>账单总额: ¥{invoice.total_amount.toLocaleString()}</p>
                <p>已付定金: ¥{invoice.deposit_amount.toLocaleString()}</p>
                <p>本次支付: ¥{processedPayment.amount.toLocaleString()}</p>
                <p>剩余余额: ¥{(invoice.remaining_balance - processedPayment.amount).toLocaleString()}</p>
              </div>

              {processedPayment.notes && (
                <div className="text-sm">
                  <p className="text-muted-foreground">备注</p>
                  <p>{processedPayment.notes}</p>
                </div>
              )}
            </div>

            {/* Footer */}
            <div className="text-center text-xs text-muted-foreground border-t pt-3 print:pt-1">
              <p>感谢您选择我们的服务</p>
              <p>如有疑问，请联系前台</p>
            </div>
          </div>

          <DialogFooter className="print:hidden">
            <Button variant="outline" onClick={printReceipt}>
              <Printer className="mr-2 h-4 w-4" />
              打印收据
            </Button>
            <Button variant="outline" onClick={sendReceiptEmail}>
              <Send className="mr-2 h-4 w-4" />
              邮件发送
            </Button>
            <Button onClick={onClose}>
              完成
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    )
  }

  return (
    <Dialog open={true} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <CreditCard className="h-5 w-5" />
            处理支付
          </DialogTitle>
          <DialogDescription>
            为账单 {invoice.invoice_number} 处理支付
          </DialogDescription>
        </DialogHeader>

        <form onSubmit={handlePaymentSubmit}>
          <div className="space-y-4">
            {/* Invoice Summary */}
            <Card>
              <CardContent className="p-4">
                <div className="space-y-2 text-sm">
                  <div className="flex justify-between">
                    <span>客户:</span>
                    <span className="font-medium">{invoice.client_name}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>账单总额:</span>
                    <span>¥{invoice.total_amount.toLocaleString()}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>已付定金:</span>
                    <span>¥{invoice.deposit_amount.toLocaleString()}</span>
                  </div>
                  <div className="flex justify-between border-t pt-2">
                    <span className="font-medium">剩余应付:</span>
                    <span className="font-bold text-lg">¥{invoice.remaining_balance.toLocaleString()}</span>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Payment Form */}
            <div className="space-y-4">
              <div>
                <Label htmlFor="amount">支付金额</Label>
                <Input
                  id="amount"
                  type="number"
                  min="0"
                  max={invoice.remaining_balance}
                  step="0.01"
                  value={paymentData.amount}
                  onChange={(e) => setPaymentData(prev => ({ 
                    ...prev, 
                    amount: parseFloat(e.target.value) || 0 
                  }))}
                  required
                />
                <div className="flex gap-2 mt-2">
                  <Button
                    type="button"
                    variant="outline"
                    size="sm"
                    onClick={() => setPaymentData(prev => ({ 
                      ...prev, 
                      amount: invoice.remaining_balance 
                    }))}
                  >
                    全额支付
                  </Button>
                  <Button
                    type="button"
                    variant="outline"
                    size="sm"
                    onClick={() => setPaymentData(prev => ({ 
                      ...prev, 
                      amount: Math.round(invoice.remaining_balance / 2) 
                    }))}
                  >
                    支付一半
                  </Button>
                </div>
              </div>

              <div>
                <Label htmlFor="payment_method">支付方式</Label>
                <Select
                  value={paymentData.payment_method}
                  onValueChange={(value) => setPaymentData(prev => ({ 
                    ...prev, 
                    payment_method: value 
                  }))}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    {paymentMethods.map((method) => {
                      const IconComponent = method.icon
                      return (
                        <SelectItem key={method.value} value={method.value}>
                          <div className="flex items-center gap-2">
                            <IconComponent className="h-4 w-4" />
                            {method.label}
                          </div>
                        </SelectItem>
                      )
                    })}
                  </SelectContent>
                </Select>
              </div>

              {(paymentData.payment_method === 'card' || 
                paymentData.payment_method === 'transfer' ||
                paymentData.payment_method === 'alipay' ||
                paymentData.payment_method === 'wechat') && (
                <div>
                  <Label htmlFor="reference_number">参考号码/交易号</Label>
                  <Input
                    id="reference_number"
                    value={paymentData.reference_number}
                    onChange={(e) => setPaymentData(prev => ({ 
                      ...prev, 
                      reference_number: e.target.value 
                    }))}
                    placeholder="输入交易号或参考号码"
                  />
                </div>
              )}

              <div>
                <Label htmlFor="notes">备注 (可选)</Label>
                <Textarea
                  id="notes"
                  value={paymentData.notes}
                  onChange={(e) => setPaymentData(prev => ({ 
                    ...prev, 
                    notes: e.target.value 
                  }))}
                  placeholder="支付备注信息..."
                  rows={3}
                />
              </div>
            </div>

            {/* Payment Summary */}
            <Card className="bg-muted/30">
              <CardContent className="p-4">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <Receipt className="h-5 w-5 text-muted-foreground" />
                    <span className="font-medium">本次收款</span>
                  </div>
                  <span className="text-xl font-bold">
                    ¥{paymentData.amount.toLocaleString()}
                  </span>
                </div>
                <div className="mt-2 text-sm text-muted-foreground">
                  支付后剩余: ¥{(invoice.remaining_balance - paymentData.amount).toLocaleString()}
                </div>
              </CardContent>
            </Card>
          </div>

          <DialogFooter className="mt-6">
            <Button type="button" variant="outline" onClick={onClose}>
              取消
            </Button>
            <Button type="submit" disabled={processing || paymentData.amount <= 0}>
              {processing ? '处理中...' : '确认收款'}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  )
}
