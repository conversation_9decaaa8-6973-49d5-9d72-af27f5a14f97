/**
 * Touch-Optimized Mobile Components
 * Collection of mobile-friendly components with touch gestures and interactions
 */

'use client'

import React, { useState, useRef, useEffect } from 'react'
import { motion, PanInfo, useAnimation } from 'framer-motion'
import { cn } from '@/lib/utils'
import { ChevronDown, ChevronUp, X, Check, RefreshCw } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Card, CardContent } from '@/components/ui/card'

/**
 * Swipeable Card Component
 */
interface SwipeableCardProps {
  children: React.ReactNode
  onSwipeLeft?: () => void
  onSwipeRight?: () => void
  leftAction?: {
    icon: React.ReactNode
    color: string
    label: string
  }
  rightAction?: {
    icon: React.ReactNode
    color: string
    label: string
  }
  className?: string
}

export const SwipeableCard: React.FC<SwipeableCardProps> = ({
  children,
  onSwipeLeft,
  onSwipeRight,
  leftAction,
  rightAction,
  className,
}) => {
  const [dragX, setDragX] = useState(0)
  const controls = useAnimation()
  const cardRef = useRef<HTMLDivElement>(null)

  const handleDragEnd = (event: any, info: PanInfo) => {
    const threshold = 100
    const velocity = info.velocity.x

    if (Math.abs(info.offset.x) > threshold || Math.abs(velocity) > 500) {
      if (info.offset.x > 0) {
        // Swiped right
        controls.start({ x: '100%', opacity: 0 })
        setTimeout(() => onSwipeRight?.(), 200)
      } else {
        // Swiped left
        controls.start({ x: '-100%', opacity: 0 })
        setTimeout(() => onSwipeLeft?.(), 200)
      }
    } else {
      // Snap back
      controls.start({ x: 0 })
      setDragX(0)
    }
  }

  const handleDrag = (event: any, info: PanInfo) => {
    setDragX(info.offset.x)
  }

  return (
    <div className="relative overflow-hidden">
      {/* Background Actions */}
      {leftAction && (
        <div
          className={cn(
            'absolute inset-y-0 left-0 flex items-center justify-start pl-4 pr-8',
            leftAction.color,
            'transform transition-transform duration-200',
            dragX > 0 ? 'translate-x-0' : '-translate-x-full'
          )}
          style={{ width: Math.max(0, dragX) }}
        >
          <div className="flex items-center space-x-2 text-white">
            {leftAction.icon}
            <span className="font-medium">{leftAction.label}</span>
          </div>
        </div>
      )}

      {rightAction && (
        <div
          className={cn(
            'absolute inset-y-0 right-0 flex items-center justify-end pr-4 pl-8',
            rightAction.color,
            'transform transition-transform duration-200',
            dragX < 0 ? 'translate-x-0' : 'translate-x-full'
          )}
          style={{ width: Math.max(0, -dragX) }}
        >
          <div className="flex items-center space-x-2 text-white">
            <span className="font-medium">{rightAction.label}</span>
            {rightAction.icon}
          </div>
        </div>
      )}

      {/* Swipeable Card */}
      <motion.div
        ref={cardRef}
        drag="x"
        dragConstraints={{ left: 0, right: 0 }}
        dragElastic={0.2}
        onDrag={handleDrag}
        onDragEnd={handleDragEnd}
        animate={controls}
        className={cn('relative z-10 bg-white', className)}
        style={{ x: dragX }}
      >
        {children}
      </motion.div>
    </div>
  )
}

/**
 * Pull-to-Refresh Component
 */
interface PullToRefreshProps {
  children: React.ReactNode
  onRefresh: () => Promise<void>
  refreshThreshold?: number
  className?: string
}

export const PullToRefresh: React.FC<PullToRefreshProps> = ({
  children,
  onRefresh,
  refreshThreshold = 80,
  className,
}) => {
  const [pullDistance, setPullDistance] = useState(0)
  const [isRefreshing, setIsRefreshing] = useState(false)
  const [canRefresh, setCanRefresh] = useState(false)
  const containerRef = useRef<HTMLDivElement>(null)

  const handleTouchStart = (e: React.TouchEvent) => {
    if (containerRef.current?.scrollTop === 0) {
      const touch = e.touches[0]
      containerRef.current.dataset.startY = touch.clientY.toString()
    }
  }

  const handleTouchMove = (e: React.TouchEvent) => {
    if (isRefreshing) return

    const container = containerRef.current
    if (!container || container.scrollTop > 0) return

    const startY = parseFloat(container.dataset.startY || '0')
    const currentY = e.touches[0].clientY
    const distance = Math.max(0, currentY - startY)

    if (distance > 0) {
      e.preventDefault()
      const adjustedDistance = Math.min(distance * 0.5, refreshThreshold * 1.5)
      setPullDistance(adjustedDistance)
      setCanRefresh(adjustedDistance >= refreshThreshold)
    }
  }

  const handleTouchEnd = async () => {
    if (canRefresh && !isRefreshing) {
      setIsRefreshing(true)
      try {
        await onRefresh()
      } finally {
        setIsRefreshing(false)
        setPullDistance(0)
        setCanRefresh(false)
      }
    } else {
      setPullDistance(0)
      setCanRefresh(false)
    }
  }

  return (
    <div
      ref={containerRef}
      className={cn('relative overflow-auto', className)}
      onTouchStart={handleTouchStart}
      onTouchMove={handleTouchMove}
      onTouchEnd={handleTouchEnd}
      style={{
        transform: `translateY(${pullDistance}px)`,
        transition: isRefreshing || pullDistance === 0 ? 'transform 0.3s ease' : 'none',
      }}
    >
      {/* Pull to Refresh Indicator */}
      <div
        className="absolute top-0 left-0 right-0 flex items-center justify-center bg-blue-50 text-blue-600 transition-all duration-300"
        style={{
          height: pullDistance,
          opacity: pullDistance > 20 ? 1 : 0,
        }}
      >
        <div className="flex items-center space-x-2">
          {isRefreshing ? (
            <>
              <RefreshCw className="h-5 w-5 animate-spin" />
              <span className="text-sm font-medium">正在刷新...</span>
            </>
          ) : canRefresh ? (
            <>
              <ChevronUp className="h-5 w-5" />
              <span className="text-sm font-medium">松开刷新</span>
            </>
          ) : (
            <>
              <ChevronDown className="h-5 w-5" />
              <span className="text-sm font-medium">下拉刷新</span>
            </>
          )}
        </div>
      </div>

      {children}
    </div>
  )
}

/**
 * Bottom Sheet Component
 */
interface BottomSheetProps {
  isOpen: boolean
  onClose: () => void
  children: React.ReactNode
  title?: string
  snapPoints?: number[]
  initialSnap?: number
}

export const BottomSheet: React.FC<BottomSheetProps> = ({
  isOpen,
  onClose,
  children,
  title,
  snapPoints = [0.3, 0.6, 0.9],
  initialSnap = 1,
}) => {
  const [currentSnap, setCurrentSnap] = useState(initialSnap)
  const controls = useAnimation()
  const sheetRef = useRef<HTMLDivElement>(null)

  useEffect(() => {
    if (isOpen) {
      controls.start({
        y: `${(1 - snapPoints[currentSnap]) * 100}%`,
        transition: { type: 'spring', damping: 30, stiffness: 300 },
      })
    } else {
      controls.start({
        y: '100%',
        transition: { type: 'spring', damping: 30, stiffness: 300 },
      })
    }
  }, [isOpen, currentSnap, snapPoints, controls])

  const handleDragEnd = (event: any, info: PanInfo) => {
    const velocity = info.velocity.y
    const currentY = info.point.y
    const windowHeight = typeof window !== "undefined" && window.innerHeight

    // Calculate which snap point to go to
    let targetSnap = currentSnap

    if (velocity > 500) {
      // Fast downward swipe
      if (currentSnap === 0) {
        onClose()
        return
      }
      targetSnap = Math.max(0, currentSnap - 1)
    } else if (velocity < -500) {
      // Fast upward swipe
      targetSnap = Math.min(snapPoints.length - 1, currentSnap + 1)
    } else {
      // Slow drag - snap to nearest point
      const currentPosition = 1 - currentY / windowHeight
      let minDistance = Infinity
      
      snapPoints.forEach((point, index) => {
        const distance = Math.abs(point - currentPosition)
        if (distance < minDistance) {
          minDistance = distance
          targetSnap = index
        }
      })
    }

    if (targetSnap === 0 && currentY > windowHeight * 0.7) {
      onClose()
    } else {
      setCurrentSnap(targetSnap)
    }
  }

  if (!isOpen) return null

  return (
    <>
      {/* Backdrop */}
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        exit={{ opacity: 0 }}
        className="fixed inset-0 bg-black bg-opacity-50 z-40"
        onClick={onClose}
      />

      {/* Bottom Sheet */}
      <motion.div
        ref={sheetRef}
        drag="y"
        dragConstraints={{ top: 0, bottom: 0 }}
        dragElastic={0.1}
        onDragEnd={handleDragEnd}
        animate={controls}
        initial={{ y: '100%' }}
        className="fixed inset-x-0 bottom-0 bg-white rounded-t-2xl shadow-2xl z-50 max-h-screen overflow-hidden"
      >
        {/* Handle */}
        <div className="flex justify-center py-3">
          <div className="w-12 h-1 bg-gray-300 rounded-full" />
        </div>

        {/* Header */}
        {title && (
          <div className="flex items-center justify-between px-6 py-3 border-b border-gray-200">
            <h2 className="text-lg font-semibold text-gray-900">{title}</h2>
            <Button variant="ghost" size="sm" onClick={onClose} className="btn-touch p-2">
              <X className="h-5 w-5" />
            </Button>
          </div>
        )}

        {/* Content */}
        <div className="overflow-auto" style={{ maxHeight: `${snapPoints[currentSnap] * 100}vh` }}>
          {children}
        </div>
      </motion.div>
    </>
  )
}

/**
 * Touch-Friendly Button Group
 */
interface TouchButtonGroupProps {
  buttons: Array<{
    label: string
    value: string
    icon?: React.ReactNode
    disabled?: boolean
  }>
  value?: string
  onChange: (value: string) => void
  className?: string
}

export const TouchButtonGroup: React.FC<TouchButtonGroupProps> = ({
  buttons,
  value,
  onChange,
  className,
}) => {
  return (
    <div className={cn('flex flex-wrap gap-2', className)}>
      {buttons.map((button) => (
        <Button
          key={button.value}
          variant={value === button.value ? 'default' : 'outline'}
          size="sm"
          disabled={button.disabled}
          onClick={() => onChange(button.value)}
          className="btn-touch flex-1 min-w-0"
        >
          {button.icon && <span className="mr-2">{button.icon}</span>}
          <span className="truncate">{button.label}</span>
        </Button>
      ))}
    </div>
  )
}

/**
 * Expandable Card Component
 */
interface ExpandableCardProps {
  title: string
  children: React.ReactNode
  defaultExpanded?: boolean
  className?: string
}

export const ExpandableCard: React.FC<ExpandableCardProps> = ({
  title,
  children,
  defaultExpanded = false,
  className,
}) => {
  const [isExpanded, setIsExpanded] = useState(defaultExpanded)

  return (
    <Card className={cn('overflow-hidden', className)}>
      <button
        onClick={() => setIsExpanded(!isExpanded)}
        className="w-full px-6 py-4 flex items-center justify-between text-left btn-touch"
      >
        <h3 className="font-semibold text-gray-900">{title}</h3>
        <motion.div
          animate={{ rotate: isExpanded ? 180 : 0 }}
          transition={{ duration: 0.2 }}
        >
          <ChevronDown className="h-5 w-5 text-gray-500" />
        </motion.div>
      </button>
      
      <motion.div
        initial={false}
        animate={{
          height: isExpanded ? 'auto' : 0,
          opacity: isExpanded ? 1 : 0,
        }}
        transition={{ duration: 0.3, ease: 'easeInOut' }}
        className="overflow-hidden"
      >
        <CardContent className="pt-0">
          {children}
        </CardContent>
      </motion.div>
    </Card>
  )
}

/**
 * Touch-Optimized Checkbox
 */
interface TouchCheckboxProps {
  checked: boolean
  onChange: (checked: boolean) => void
  label: string
  description?: string
  disabled?: boolean
  className?: string
}

export const TouchCheckbox: React.FC<TouchCheckboxProps> = ({
  checked,
  onChange,
  label,
  description,
  disabled = false,
  className,
}) => {
  return (
    <button
      onClick={() => !disabled && onChange(!checked)}
      disabled={disabled}
      className={cn(
        'flex items-start space-x-3 p-4 rounded-lg border transition-colors btn-touch text-left w-full',
        checked
          ? 'bg-blue-50 border-blue-200'
          : 'bg-white border-gray-200 hover:bg-gray-50',
        disabled && 'opacity-50 cursor-not-allowed',
        className
      )}
    >
      <div
        className={cn(
          'flex-shrink-0 w-6 h-6 rounded border-2 flex items-center justify-center transition-colors',
          checked
            ? 'bg-blue-600 border-blue-600'
            : 'border-gray-300'
        )}
      >
        {checked && <Check className="h-4 w-4 text-white" />}
      </div>
      
      <div className="flex-1 min-w-0">
        <p className="font-medium text-gray-900">{label}</p>
        {description && (
          <p className="text-sm text-gray-500 mt-1">{description}</p>
        )}
      </div>
    </button>
  )
}
