'use client'

import { useState, useEffect } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { 
  Users, 
  Calendar, 
  DollarSign, 
  TrendingUp, 
  Clock,
  AlertCircle,
  CheckCircle,
  XCircle
} from 'lucide-react'

interface DashboardStats {
  totalClients: number
  activeClients: number
  todayAppointments: number
  upcomingAppointments: number
  pendingInvoices: number
  totalRevenue: number
  consultationFeesSaved: number
  depositsPaid: number
}

interface RecentActivity {
  id: string
  type: 'appointment' | 'payment' | 'consultation_waiver'
  description: string
  timestamp: string
  status: 'success' | 'warning' | 'info'
}

export default function CRMSummaryCard() {
  const [stats, setStats] = useState<DashboardStats>({
    totalClients: 0,
    activeClients: 0,
    todayAppointments: 0,
    upcomingAppointments: 0,
    pendingInvoices: 0,
    totalRevenue: 0,
    consultationFeesSaved: 0,
    depositsPaid: 0
  })
  const [recentActivities, setRecentActivities] = useState<RecentActivity[]>([])
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    fetchDashboardData()
  }, [])

  const fetchDashboardData = async () => {
    try {
      setLoading(true)
      
      // Fetch clients data
      const clientsResponse = await fetch('/api/clients')
      const clientsData = await clientsResponse.json()
      const clients = clientsData.clients || []
      
      // Fetch appointments data
      const today = new Date().toISOString().split('T')[0]
      const tomorrow = new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString().split('T')[0]
      const nextWeek = new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString().split('T')[0]
      
      const appointmentsResponse = await fetch(`/api/appointments?start_date=${today}&end_date=${nextWeek}`)
      const appointmentsData = await appointmentsResponse.json()
      const appointments = appointmentsData.appointments || []
      
      // Fetch invoices data
      const invoicesResponse = await fetch('/api/invoices')
      const invoicesData = await invoicesResponse.json()
      const invoices = invoicesData.invoices || []
      
      // Fetch payments data
      const paymentsResponse = await fetch('/api/payments')
      const paymentsData = await paymentsResponse.json()
      const payments = paymentsData.payments || []
      
      // Calculate stats
      const todayAppointments = appointments.filter((apt: any) => apt.appointment_date === today)
      const upcomingAppointments = appointments.filter((apt: any) => apt.appointment_date > today)
      const pendingInvoices = invoices.filter((inv: any) => inv.status === 'deposit_pending')
      const totalRevenue = payments.reduce((sum: number, payment: any) => sum + payment.amount, 0)
      const consultationFeesSaved = invoices
        .filter((inv: any) => inv.consultation_fee_waived)
        .reduce((sum: number, inv: any) => sum + (inv.original_consultation_fee || 0), 0)
      const depositsPaid = payments
        .filter((payment: any) => payment.payment_type === 'deposit')
        .reduce((sum: number, payment: any) => sum + payment.amount, 0)
      
      setStats({
        totalClients: clients.length,
        activeClients: clients.filter((client: any) => client.status === 'active').length,
        todayAppointments: todayAppointments.length,
        upcomingAppointments: upcomingAppointments.length,
        pendingInvoices: pendingInvoices.length,
        totalRevenue,
        consultationFeesSaved,
        depositsPaid
      })
      
      // Generate recent activities
      const activities: RecentActivity[] = []
      
      // Recent appointments
      appointments.slice(0, 3).forEach((apt: any) => {
        activities.push({
          id: apt.id,
          type: 'appointment',
          description: `${apt.clients?.first_name} ${apt.clients?.last_name} - ${apt.treatments?.name_chinese}`,
          timestamp: apt.appointment_date,
          status: apt.status === 'completed' ? 'success' : 'info'
        })
      })
      
      // Recent payments
      payments.slice(0, 2).forEach((payment: any) => {
        activities.push({
          id: payment.id,
          type: 'payment',
          description: `收到付款 ¥${payment.amount} - ${payment.clients?.first_name} ${payment.clients?.last_name}`,
          timestamp: payment.payment_date,
          status: 'success'
        })
      })
      
      // Recent consultation fee waivers
      invoices
        .filter((inv: any) => inv.consultation_fee_waived)
        .slice(0, 2)
        .forEach((inv: any) => {
          activities.push({
            id: inv.id,
            type: 'consultation_waiver',
            description: `咨询费减免 ¥${inv.original_consultation_fee} - ${inv.clients?.first_name} ${inv.clients?.last_name}`,
            timestamp: inv.updated_at,
            status: 'info'
          })
        })
      
      setRecentActivities(activities.sort((a, b) => 
        new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime()
      ).slice(0, 5))
      
    } catch (error) {
      console.error('Error fetching dashboard data:', error)
    } finally {
      setLoading(false)
    }
  }

  const getActivityIcon = (type: string, status: string) => {
    switch (type) {
      case 'appointment':
        return status === 'success' ? <CheckCircle className="h-4 w-4 text-green-500" /> : <Clock className="h-4 w-4 text-blue-500" />
      case 'payment':
        return <DollarSign className="h-4 w-4 text-green-500" />
      case 'consultation_waiver':
        return <TrendingUp className="h-4 w-4 text-orange-500" />
      default:
        return <AlertCircle className="h-4 w-4 text-gray-500" />
    }
  }

  if (loading) {
    return (
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {[...Array(4)].map((_, i) => (
          <Card key={i} className="animate-pulse">
            <CardHeader className="pb-2">
              <div className="h-4 bg-gray-200 rounded w-3/4"></div>
            </CardHeader>
            <CardContent>
              <div className="h-8 bg-gray-200 rounded w-1/2 mb-2"></div>
              <div className="h-3 bg-gray-200 rounded w-full"></div>
            </CardContent>
          </Card>
        ))}
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">总客户数</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.totalClients}</div>
            <p className="text-xs text-muted-foreground">
              活跃客户: {stats.activeClients}
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">今日预约</CardTitle>
            <Calendar className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.todayAppointments}</div>
            <p className="text-xs text-muted-foreground">
              即将到来: {stats.upcomingAppointments}
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">待付账单</CardTitle>
            <AlertCircle className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-orange-600">{stats.pendingInvoices}</div>
            <p className="text-xs text-muted-foreground">
              需要跟进
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">总收入</CardTitle>
            <DollarSign className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600">¥{stats.totalRevenue.toFixed(2)}</div>
            <p className="text-xs text-muted-foreground">
              定金: ¥{stats.depositsPaid.toFixed(2)}
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Additional Stats */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle className="text-lg">咨询费减免统计</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-3xl font-bold text-blue-600 mb-2">
              ¥{stats.consultationFeesSaved.toFixed(2)}
            </div>
            <p className="text-sm text-muted-foreground">
              通过治疗转化为客户节省的咨询费用
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="text-lg">最近活动</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {recentActivities.length > 0 ? (
                recentActivities.map((activity) => (
                  <div key={activity.id} className="flex items-center space-x-3">
                    {getActivityIcon(activity.type, activity.status)}
                    <div className="flex-1 min-w-0">
                      <p className="text-sm font-medium truncate">
                        {activity.description}
                      </p>
                      <p className="text-xs text-muted-foreground">
                        {new Date(activity.timestamp).toLocaleDateString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        timeZone: 'Asia/Shanghai'
      })}
                      </p>
                    </div>
                  </div>
                ))
              ) : (
                <p className="text-sm text-muted-foreground">暂无最近活动</p>
              )}
            </div>
            <Button 
              variant="outline" 
              size="sm" 
              className="w-full mt-4"
              onClick={fetchDashboardData}
            >
              刷新数据
            </Button>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
