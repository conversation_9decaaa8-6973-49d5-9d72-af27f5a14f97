'use client'

import { useState } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { 
  UserPlus, 
  Calendar, 
  Receipt, 
  CreditCard,
  Stethoscope,
  Search,
  Clock,
  AlertTriangle
} from 'lucide-react'
import { useRouter } from 'next/navigation'
import AppointmentModal from '@/components/modals/AppointmentModal'
import ClientModal from '@/components/modals/ClientModal'
import InvoiceModal from '@/components/modals/InvoiceModal'
import PaymentModal from '@/components/modals/PaymentModal'
import TreatmentModal from '@/components/modals/TreatmentModal'

export default function QuickActionsToolbar() {
  const router = useRouter()
  const [activeModal, setActiveModal] = useState<string | null>(null)

  const closeModal = () => setActiveModal(null)
  const handleSuccess = () => {
    closeModal()
    // Refresh the page or update data
    typeof window !== "undefined" && window.location.reload()
  }

  const quickActions = [
    {
      id: 'new-appointment',
      title: '新建预约',
      description: '为客户创建新的预约',
      icon: Calendar,
      color: 'bg-blue-500 hover:bg-blue-600',
      action: () => setActiveModal('appointment')
    },
    {
      id: 'new-client',
      title: '新建客户',
      description: '添加新的客户信息',
      icon: UserPlus,
      color: 'bg-green-500 hover:bg-green-600',
      action: () => setActiveModal('client')
    },
    {
      id: 'new-invoice',
      title: '新建账单',
      description: '创建新的账单',
      icon: Receipt,
      color: 'bg-orange-500 hover:bg-orange-600',
      action: () => setActiveModal('invoice')
    },
    {
      id: 'new-payment',
      title: '记录付款',
      description: '记录客户付款',
      icon: CreditCard,
      color: 'bg-purple-500 hover:bg-purple-600',
      action: () => setActiveModal('payment')
    },
    {
      id: 'new-treatment',
      title: '新建治疗项目',
      description: '添加新的治疗服务',
      icon: Stethoscope,
      color: 'bg-teal-500 hover:bg-teal-600',
      action: () => setActiveModal('treatment')
    }
  ]

  const navigationActions = [
    {
      id: 'calendar',
      title: '预约日历',
      description: '查看预约安排',
      icon: Calendar,
      action: () => router.push('/dashboard/calendar')
    },
    {
      id: 'clients',
      title: '客户管理',
      description: '管理客户信息',
      icon: UserPlus,
      action: () => router.push('/dashboard/clients')
    },
    {
      id: 'treatments',
      title: '治疗项目',
      description: '管理治疗服务',
      icon: Stethoscope,
      action: () => router.push('/dashboard/treatments')
    },
    {
      id: 'invoices',
      title: '账单管理',
      description: '查看和管理账单',
      icon: Receipt,
      action: () => router.push('/dashboard/invoices')
    },
    {
      id: 'payments',
      title: '付款记录',
      description: '查看付款历史',
      icon: CreditCard,
      action: () => router.push('/dashboard/payments')
    }
  ]

  return (
    <>
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Quick Actions */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Clock className="h-5 w-5" />
              快速操作
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-3">
              {quickActions.map((action) => (
                <Button
                  key={action.id}
                  variant="outline"
                  className={`h-auto p-4 flex flex-col items-start space-y-2 text-left hover:text-white ${action.color}`}
                  onClick={action.action}
                >
                  <action.icon className="h-5 w-5" />
                  <div>
                    <div className="font-medium">{action.title}</div>
                    <div className="text-xs opacity-90">{action.description}</div>
                  </div>
                </Button>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Navigation */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Search className="h-5 w-5" />
              快速导航
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-3">
              {navigationActions.map((action) => (
                <Button
                  key={action.id}
                  variant="ghost"
                  className="h-auto p-4 flex flex-col items-start space-y-2 text-left hover:bg-gray-100"
                  onClick={action.action}
                >
                  <action.icon className="h-5 w-5 text-gray-600" />
                  <div>
                    <div className="font-medium text-gray-900">{action.title}</div>
                    <div className="text-xs text-gray-500">{action.description}</div>
                  </div>
                </Button>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Important Reminders */}
      <Card className="mt-6">
        <CardHeader>
          <CardTitle className="flex items-center gap-2 text-orange-600">
            <AlertTriangle className="h-5 w-5" />
            重要提醒
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-3 text-sm">
            <div className="flex items-start gap-3 p-3 bg-blue-50 rounded-lg">
              <div className="w-2 h-2 bg-blue-500 rounded-full mt-2"></div>
              <div>
                <div className="font-medium text-blue-900">固定定金系统</div>
                <div className="text-blue-700">现在使用治疗项目的固定定金金额，不再按百分比计算</div>
              </div>
            </div>
            <div className="flex items-start gap-3 p-3 bg-green-50 rounded-lg">
              <div className="w-2 h-2 bg-green-500 rounded-full mt-2"></div>
              <div>
                <div className="font-medium text-green-900">咨询费减免</div>
                <div className="text-green-700">客户进行治疗时，咨询费将自动减免</div>
              </div>
            </div>
            <div className="flex items-start gap-3 p-3 bg-purple-50 rounded-lg">
              <div className="w-2 h-2 bg-purple-500 rounded-full mt-2"></div>
              <div>
                <div className="font-medium text-purple-900">一天一个定金</div>
                <div className="text-purple-700">同一天多项治疗只收取一个定金（取最高金额）</div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Modals */}
      <AppointmentModal
        isOpen={activeModal === 'appointment'}
        onClose={closeModal}
        onSuccess={handleSuccess}
      />
      <ClientModal
        isOpen={activeModal === 'client'}
        onClose={closeModal}
        onSuccess={handleSuccess}
        mode="create"
      />
      <InvoiceModal
        isOpen={activeModal === 'invoice'}
        onClose={closeModal}
        onSuccess={handleSuccess}
      />
      <PaymentModal
        isOpen={activeModal === 'payment'}
        onClose={closeModal}
        onSuccess={handleSuccess}
      />
      <TreatmentModal
        isOpen={activeModal === 'treatment'}
        onClose={closeModal}
        onSuccess={handleSuccess}
        mode="create"
      />
    </>
  )
}
