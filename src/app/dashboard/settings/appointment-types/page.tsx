'use client'

import { useState, useEffect } from 'react'
import { <PERSON><PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { 
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger
} from '@/components/ui/dialog'
import { 
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow
} from '@/components/ui/table'
import { Plus, Edit, Trash2, Calendar } from 'lucide-react'
import { showSuccessToast, showErrorToast } from '@/lib/toast-utils'
import PageContainer from '@/components/layout/page-container'

interface AppointmentType {
  id: string
  name: string
  description?: string
  color: string
  created_at: string
}

// 默认预约类型
const DEFAULT_TYPES: AppointmentType[] = [
  {
    id: 'consultation',
    name: '咨询',
    description: '初次咨询或问诊',
    color: '#8b5cf6',
    created_at: new Date().toISOString()
  },
  {
    id: 'treatment',
    name: '治疗',
    description: '正式治疗项目',
    color: '#10b981',
    created_at: new Date().toISOString()
  },
  {
    id: 'follow_up',
    name: '复诊',
    description: '治疗后复查',
    color: '#f59e0b',
    created_at: new Date().toISOString()
  },
  {
    id: 'maintenance',
    name: '维护',
    description: '定期维护保养',
    color: '#6b7280',
    created_at: new Date().toISOString()
  }
]

export default function AppointmentTypesPage() {
  const [types, setTypes] = useState<AppointmentType[]>([])
  const [isDialogOpen, setIsDialogOpen] = useState(false)
  const [editingType, setEditingType] = useState<AppointmentType | null>(null)
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    color: '#3b82f6'
  })
  const [loading, setLoading] = useState(false)

  // 加载预约类型
  useEffect(() => {
    loadTypes()
  }, [])

  const loadTypes = () => {
    try {
      const saved = typeof window !== "undefined" && localStorage.getItem('appointment_types')
      if (saved) {
        const customTypes = JSON.parse(saved)
        setTypes([...DEFAULT_TYPES, ...customTypes])
      } else {
        setTypes(DEFAULT_TYPES)
      }
    } catch (error) {
      console.error('Error loading appointment types:', error)
      setTypes(DEFAULT_TYPES)
    }
  }

  const saveCustomTypes = (customTypes: AppointmentType[]) => {
    try {
      typeof window !== "undefined" && localStorage.setItem('appointment_types', JSON.stringify(customTypes))
    } catch (error) {
      console.error('Error saving appointment types:', error)
      showErrorToast('保存失败', '请稍后重试')
    }
  }

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!formData.name.trim()) {
      showErrorToast('请输入预约类型名称')
      return
    }

    // 检查名称是否重复
    const isDuplicate = types.some(type => 
      type.name === formData.name.trim() && type.id !== editingType?.id
    )
    
    if (isDuplicate) {
      showErrorToast('预约类型名称已存在')
      return
    }

    setLoading(true)

    try {
      if (editingType) {
        // 更新预约类型
        const updatedTypes = types.map(type =>
          type.id === editingType.id
            ? { ...type, ...formData, name: formData.name.trim() }
            : type
        )
        setTypes(updatedTypes)
        
        // 只保存自定义类型
        const customTypes = updatedTypes.filter(type => type.id.startsWith('custom_'))
        saveCustomTypes(customTypes)
        
        showSuccessToast('预约类型更新成功')
      } else {
        // 新建预约类型
        const newType: AppointmentType = {
          id: `custom_${Date.now()}`,
          name: formData.name.trim(),
          description: formData.description.trim(),
          color: formData.color,
          created_at: new Date().toISOString()
        }
        
        const updatedTypes = [...types, newType]
        setTypes(updatedTypes)
        
        // 只保存自定义类型
        const customTypes = updatedTypes.filter(type => type.id.startsWith('custom_'))
        saveCustomTypes(customTypes)
        
        showSuccessToast('预约类型创建成功')
      }

      handleCloseDialog()
    } catch (error) {
      showErrorToast('操作失败', '请稍后重试')
    } finally {
      setLoading(false)
    }
  }

  const handleEdit = (type: AppointmentType) => {
    setEditingType(type)
    setFormData({
      name: type.name,
      description: type.description || '',
      color: type.color
    })
    setIsDialogOpen(true)
  }

  const handleDelete = (typeId: string) => {
    if (!typeId.startsWith('custom_')) {
      showErrorToast('无法删除默认预约类型')
      return
    }

    if (!confirm('确定要删除这个预约类型吗？删除后无法恢复。')) {
      return
    }

    const updatedTypes = types.filter(type => type.id !== typeId)
    setTypes(updatedTypes)
    
    // 只保存自定义类型
    const customTypes = updatedTypes.filter(type => type.id.startsWith('custom_'))
    saveCustomTypes(customTypes)
    
    showSuccessToast('预约类型删除成功')
  }

  const handleCloseDialog = () => {
    setIsDialogOpen(false)
    setEditingType(null)
    setFormData({
      name: '',
      description: '',
      color: '#3b82f6'
    })
  }

  return (
    <PageContainer>
      <div className='flex flex-1 flex-col space-y-4'>
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">预约类型管理</h1>
          <p className="text-muted-foreground">
            管理预约类型，便于分类和识别不同类型的预约
          </p>
        </div>
        <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
          <DialogTrigger asChild>
            <Button>
              <Plus className="mr-2 h-4 w-4" />
              新建类型
            </Button>
          </DialogTrigger>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>
                {editingType ? '编辑预约类型' : '新建预约类型'}
              </DialogTitle>
            </DialogHeader>
            <form onSubmit={handleSubmit} className="space-y-4">
              <div className="space-y-2">
                <Label>类型名称 *</Label>
                <Input
                  value={formData.name}
                  onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
                  placeholder="输入预约类型名称"
                  required
                />
              </div>
              
              <div className="space-y-2">
                <Label>描述</Label>
                <Textarea
                  value={formData.description}
                  onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
                  placeholder="输入类型描述（可选）"
                  rows={3}
                />
              </div>
              
              <div className="space-y-2">
                <Label>颜色</Label>
                <div className="flex items-center gap-2">
                  <Input
                    type="color"
                    value={formData.color}
                    onChange={(e) => setFormData(prev => ({ ...prev, color: e.target.value }))}
                    className="w-16 h-10"
                  />
                  <Input
                    value={formData.color}
                    onChange={(e) => setFormData(prev => ({ ...prev, color: e.target.value }))}
                    placeholder="#3b82f6"
                    className="flex-1"
                  />
                </div>
              </div>
              
              <div className="flex justify-end gap-2 pt-4">
                <Button type="button" variant="outline" onClick={handleCloseDialog}>
                  取消
                </Button>
                <Button type="submit" disabled={loading}>
                  {loading ? '保存中...' : '保存'}
                </Button>
              </div>
            </form>
          </DialogContent>
        </Dialog>
      </div>

      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Calendar className="h-5 w-5" />
            预约类型列表
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="table-container">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>颜色</TableHead>
                <TableHead>名称</TableHead>
                <TableHead>描述</TableHead>
                <TableHead>类型</TableHead>
                <TableHead>创建时间</TableHead>
                <TableHead className="text-right">操作</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {types.map((type) => (
                <TableRow key={type.id}>
                  <TableCell>
                    <div 
                      className="w-6 h-6 rounded-full border"
                      style={{ backgroundColor: type.color }}
                    />
                  </TableCell>
                  <TableCell className="font-medium">{type.name}</TableCell>
                  <TableCell className="text-muted-foreground">
                    {type.description || '无描述'}
                  </TableCell>
                  <TableCell>
                    {type.id.startsWith('custom_') ? (
                      <span className="text-blue-600">自定义</span>
                    ) : (
                      <span className="text-gray-600">默认</span>
                    )}
                  </TableCell>
                  <TableCell>
                    {new Date(type.created_at).toLocaleDateString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        timeZone: 'Asia/Shanghai'
      })}
                  </TableCell>
                  <TableCell className="text-right">
                    <div className="flex items-center justify-end gap-2">
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => handleEdit(type)}
                      >
                        <Edit className="h-4 w-4" />
                      </Button>
                      {type.id.startsWith('custom_') && (
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handleDelete(type.id)}
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      )}
                    </div>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
          </div>
        </CardContent>
      </Card>
      </div>
    </PageContainer>
  )
}
