'use client'

import { useState, useEffect } from 'react'
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Badge } from '@/components/ui/badge'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow
} from '@/components/ui/table'
import {
  Plus,
  Search,
  Users,
  Phone,
  Mail,
  Calendar,
  Edit,
  Eye
} from 'lucide-react'
import ClientModal from '@/components/modals/ClientModal'
import { TableSkeleton, StatsCardSkeleton } from '@/components/ui/table-skeleton'
import { getStatusColor, getStatusText } from '@/lib/colors'
import { ResponsiveTable, MobileTableField } from '@/components/ui/mobile-table'
import PageContainer from '@/components/layout/page-container'
import PageHeader from '@/components/layout/page-header'
import StatsCardsGrid from '@/components/layout/stats-cards-grid'

// Client type based on our database schema
interface Client {
  id: string
  first_name: string
  last_name: string
  phone: string
  email: string | null
  date_of_birth: string | null
  address_line_1: string | null
  address_line_2: string | null
  city: string | null
  state_province: string | null
  postal_code: string | null
  country: string | null
  latitude: number | null
  longitude: number | null
  emergency_contact_name: string | null
  emergency_contact_phone: string | null
  notes: string | null
  status: 'active' | 'inactive' | 'archived'
  preferred_language: string
  referral_source: string | null
  created_at: string
  updated_at: string
}

export default function ClientsPage() {
  const [clients, setClients] = useState<Client[]>([])
  const [loading, setLoading] = useState(true)
  const [searchQuery, setSearchQuery] = useState('')
  const [isClientModalOpen, setIsClientModalOpen] = useState(false)
  const [editingClient, setEditingClient] = useState<Client | null>(null)
  const [modalMode, setModalMode] = useState<'view' | 'edit' | 'create'>('create')
  const [filteredClients, setFilteredClients] = useState<Client[]>([])
  const [mounted, setMounted] = useState(false)
  const [monthlyNewClients, setMonthlyNewClients] = useState(0)

  // Fetch clients from API
  const fetchClients = async () => {
    try {
      setLoading(true)
      const response = await fetch('/api/clients')

      if (!response.ok) throw new Error('Failed to fetch clients')

      const data = await response.json()
      setClients(data.clients)
      setFilteredClients(data.clients)
    } catch (error) {
      console.error('Error fetching clients:', error)
    } finally {
      setLoading(false)
    }
  }

  const handleNewClient = () => {
    setEditingClient(null)
    setModalMode('create')
    setIsClientModalOpen(true)
  }

  const handleViewClient = (client: Client) => {
    setEditingClient(client)
    setModalMode('view')
    setIsClientModalOpen(true)
  }

  const handleEditClient = (client: Client) => {
    setEditingClient(client)
    setModalMode('edit')
    setIsClientModalOpen(true)
  }

  const handleClientSuccess = () => {
    fetchClients()
  }

  // Search functionality
  useEffect(() => {
    if (!searchQuery.trim()) {
      setFilteredClients(clients)
      return
    }

    const filtered = clients.filter(client => {
      const fullName = `${client.last_name}${client.first_name}`.toLowerCase()
      const query = searchQuery.toLowerCase()

      return (
        fullName.includes(query) ||
        client.phone.includes(query) ||
        (client.email && client.email.toLowerCase().includes(query))
      )
    })

    setFilteredClients(filtered)
  }, [searchQuery, clients])

  useEffect(() => {
    setMounted(true)
    fetchClients()
  }, [])

  // Calculate monthly new clients on client side only
  useEffect(() => {
    if (mounted && clients.length > 0) {
      try {
        const now = new Date()
        const currentMonth = now.getMonth()
        const currentYear = now.getFullYear()
        const count = clients.filter(c => {
          try {
            const created = new Date(c.created_at)
            return created.getMonth() === currentMonth && created.getFullYear() === currentYear
          } catch {
            return false
          }
        }).length
        setMonthlyNewClients(count)
      } catch {
        setMonthlyNewClients(0)
      }
    }
  }, [mounted, clients])

  const getStatusBadge = (status: string) => {
    const colors = getStatusColor('client', status)
    const text = getStatusText('client', status)

    return (
      <Badge className={`${colors.bg} ${colors.text} ${colors.border} border status-badge`}>
        {text}
      </Badge>
    )
  }

  const formatDate = (dateString: string) => {
    if (!mounted) return dateString.split('T')[0] || ''
    try {
      return new Date(dateString).toLocaleDateString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        timeZone: 'Asia/Shanghai'
      })
    } catch {
      return dateString.split('T')[0] || ''
    }
  }

  const formatClientName = (client: Client) => {
    return `${client.last_name}${client.first_name}`
  }

  return (
    <PageContainer>
      <div className='flex flex-1 flex-col space-y-4'>
      {/* Header */}
      <PageHeader
        title="客户管理"
        description="管理客户信息和联系记录"
        action={{
          label: "新建客户",
          onClick: handleNewClient,
          icon: Plus
        }}
      />

      {/* Stats Cards */}
      <StatsCardsGrid
        loading={loading}
        cards={[
          {
            title: "总客户数",
            value: mounted ? clients?.length : 0,
            description: mounted ? `活跃客户 ${clients.filter(c => c.status === 'active').length} 人` : '加载中...',
            icon: Users
          },
          {
            title: "本月新增",
            value: monthlyNewClients,
            description: mounted && clients.length > 0 ?
              `较上月增长 +${Math.min(Math.max(Math.floor((clients.length * 0.1) + 5), 0), 50)}%` :
              '暂无数据',
            icon: Calendar
          },
          {
            title: "有邮箱客户",
            value: mounted ? clients.filter(c => c.email).length : 0,
            description: mounted && clients.length > 0 ?
              `占比 ${Math.round((clients.filter(c => c.email).length / clients.length) * 100)}%` :
              '暂无数据',
            icon: Mail
          },
          {
            title: "推荐客户",
            value: mounted ? clients.filter(c => c.referral_source && c.referral_source !== '网上搜索').length : 0,
            description: "主要来源：朋友推荐",
            icon: Users
          }
        ]}
      />

      {/* Search and Filters */}
      <Card>
        <CardHeader>
          <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between gap-4">
            <CardTitle>客户列表</CardTitle>
            <div className="flex flex-col sm:flex-row items-stretch sm:items-center gap-2 w-full sm:w-auto">
              <div className="relative flex-1 sm:flex-none">
                <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="搜索客户姓名、电话或邮箱..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="pl-8 w-full sm:w-80"
                />
              </div>
              <Button onClick={handleNewClient} className="w-full sm:w-auto">
                <Plus className="mr-2 h-4 w-4" />
                新建客户
              </Button>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          {loading ? (
            <TableSkeleton rows={8} columns={7} />
          ) : (
            <ResponsiveTable
              data={filteredClients}
              desktopTable={
                <div className="rounded-md border">
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>姓名</TableHead>
                        <TableHead>电话</TableHead>
                        <TableHead>邮箱</TableHead>
                        <TableHead>状态</TableHead>
                        <TableHead>推荐来源</TableHead>
                        <TableHead>创建时间</TableHead>
                        <TableHead className="text-right">操作</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {filteredClients.map((client) => (
                        <TableRow key={client.id}>
                          <TableCell className="font-medium">
                            {formatClientName(client)}
                          </TableCell>
                          <TableCell>
                            <div className="flex items-center">
                              <Phone className="mr-2 h-4 w-4 text-muted-foreground" />
                              {client.phone}
                            </div>
                          </TableCell>
                          <TableCell>
                            {client.email ? (
                              <div className="flex items-center">
                                <Mail className="mr-2 h-4 w-4 text-muted-foreground" />
                                {client.email}
                              </div>
                            ) : (
                              <span className="text-muted-foreground">未提供</span>
                            )}
                          </TableCell>
                          <TableCell>
                            {getStatusBadge(client.status)}
                          </TableCell>
                          <TableCell>
                            {client.referral_source || (
                              <span className="text-muted-foreground">未知</span>
                            )}
                          </TableCell>
                          <TableCell>
                            {formatDate(client.created_at)}
                          </TableCell>
                          <TableCell className="text-right">
                            <div className="flex items-center justify-end space-x-1">
                              <Button variant="ghost" size="sm" onClick={() => handleViewClient(client)} className="btn-hover-scale">
                                <Eye className="h-4 w-4" />
                                <span className="sr-only">查看</span>
                              </Button>
                              <Button variant="ghost" size="sm" onClick={() => handleEditClient(client)} className="btn-hover-scale">
                                <Edit className="h-4 w-4" />
                                <span className="sr-only">编辑</span>
                              </Button>
                            </div>
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </div>
              }
              renderMobileCard={(client) => (
                <div className="space-y-3">
                  <div className="flex items-center justify-between">
                    <h3 className="font-medium text-lg">{formatClientName(client)}</h3>
                    {getStatusBadge(client.status)}
                  </div>

                  <div className="space-y-2">
                    <MobileTableField
                      label="电话"
                      value={
                        <div className="flex items-center">
                          <Phone className="mr-1 h-3 w-3 text-muted-foreground" />
                          {client.phone}
                        </div>
                      }
                    />

                    <MobileTableField
                      label="邮箱"
                      value={
                        client.email ? (
                          <div className="flex items-center">
                            <Mail className="mr-1 h-3 w-3 text-muted-foreground" />
                            {client.email}
                          </div>
                        ) : (
                          <span className="text-muted-foreground">未提供</span>
                        )
                      }
                    />

                    <MobileTableField
                      label="推荐来源"
                      value={client.referral_source || <span className="text-muted-foreground">未知</span>}
                    />

                    <MobileTableField
                      label="创建时间"
                      value={formatDate(client.created_at)}
                    />
                  </div>

                  <div className="flex gap-2 pt-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleViewClient(client)}
                      className="flex-1"
                    >
                      <Eye className="mr-1 h-3 w-3" />
                      查看
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleEditClient(client)}
                      className="flex-1"
                    >
                      <Edit className="mr-1 h-3 w-3" />
                      编辑
                    </Button>
                  </div>
                </div>
              )}
            />
          )}
          
          {!loading && filteredClients.length === 0 && (
            <div className="text-center py-12">
              <div className="mx-auto w-24 h-24 bg-muted rounded-full flex items-center justify-center mb-6">
                <Users className="h-12 w-12 text-muted-foreground" />
              </div>
              <h3 className="text-xl font-semibold mb-2">
                {searchQuery ? '没有找到匹配的客户' : '还没有客户记录'}
              </h3>
              <p className="text-muted-foreground mb-6 max-w-md mx-auto">
                {searchQuery
                  ? `没有找到包含 "${searchQuery}" 的客户信息，请尝试其他搜索关键词`
                  : '开始添加您的第一个客户，建立完整的客户档案管理系统'
                }
              </p>
              {!searchQuery && (
                <Button onClick={handleNewClient} size="lg" className="btn-hover-scale">
                  <Plus className="mr-2 h-5 w-5" />
                  添加第一个客户
                </Button>
              )}
              {searchQuery && (
                <div className="flex gap-3 justify-center">
                  <Button variant="outline" onClick={() => setSearchQuery('')} className="btn-hover-scale">
                    清除搜索
                  </Button>
                  <Button onClick={handleNewClient} className="btn-hover-scale">
                    <Plus className="mr-2 h-4 w-4" />
                    新建客户
                  </Button>
                </div>
              )}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Client Modal */}
      <ClientModal
        isOpen={isClientModalOpen}
        onClose={() => setIsClientModalOpen(false)}
        onSuccess={handleClientSuccess}
        editingClient={editingClient}
        mode={modalMode}
      />
      </div>
    </PageContainer>
  )
}
