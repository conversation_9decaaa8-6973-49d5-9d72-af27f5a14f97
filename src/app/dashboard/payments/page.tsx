'use client'

import { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table'
import { IconPlus, IconSearch, IconCreditCard, IconCash, IconBuildingBank, IconClock, IconEdit, IconTrash } from '@tabler/icons-react'
import PageContainer from '@/components/layout/page-container'

import PaymentModal from '@/components/modals/PaymentModal'
import { ResponsiveTable, MobileTableField } from '@/components/ui/mobile-table'
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle } from '@/components/ui/alert-dialog'
import { showSuccessToast, showErrorToast } from '@/lib/toast-utils'


interface Payment {
  id: string
  amount: number
  payment_date: string
  payment_method: 'cash' | 'credit_card' | 'debit_card' | 'bank_transfer' | 'check' | 'other'
  payment_type: 'deposit' | 'full_payment' | 'partial_payment'
  reference_number: string | null
  notes: string | null
  invoices: {
    invoice_number: string
    treatment_date: string
    total_amount: number
  }
  clients: {
    first_name: string
    last_name: string
    phone: string
  }
}

const paymentMethodConfig = {
  cash: { label: '现金', icon: IconCash },
  credit_card: { label: '信用卡', icon: IconCreditCard },
  debit_card: { label: '借记卡', icon: IconCreditCard },
  bank_transfer: { label: '银行转账', icon: IconBuildingBank },
  check: { label: '支票', icon: IconCreditCard },
  other: { label: '其他', icon: IconCreditCard }
}

const paymentTypeConfig = {
  deposit: { label: '定金', color: 'bg-blue-100 text-blue-800' },
  partial_payment: { label: '部分付款', color: 'bg-yellow-100 text-yellow-800' },
  full_payment: { label: '全额付款', color: 'bg-green-100 text-green-800' }
}

export default function PaymentsPage() {
  const [payments, setPayments] = useState<Payment[]>([])
  const [loading, setLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState('')
  const [methodFilter, setMethodFilter] = useState<string>('all')
  const [typeFilter, setTypeFilter] = useState<string>('all')

  // Modal states
  const [isPaymentModalOpen, setIsPaymentModalOpen] = useState(false)
  const [editingPayment, setEditingPayment] = useState<any>(null)
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false)
  const [paymentToDelete, setPaymentToDelete] = useState<Payment | null>(null)
  const [isDeleting, setIsDeleting] = useState(false)

  useEffect(() => {
    fetchPayments()
  }, [])

  const fetchPayments = async () => {
    try {
      const response = await fetch('/api/payments')
      const data = await response.json()
      setPayments(data.payments || [])
    } catch (error) {
      console.error('Error fetching payments:', error)
    } finally {
      setLoading(false)
    }
  }

  const handleNewPayment = () => {
    setEditingPayment(null)
    setIsPaymentModalOpen(true)
  }

  const handlePaymentSuccess = () => {
    fetchPayments()
  }

  const handleEditPayment = (payment: Payment) => {
    setEditingPayment(payment)
    setIsPaymentModalOpen(true)
  }

  const handleDeletePayment = (payment: Payment) => {
    setPaymentToDelete(payment)
    setDeleteDialogOpen(true)
  }

  const confirmDeletePayment = async () => {
    if (!paymentToDelete) return

    setIsDeleting(true)
    try {
      const response = await fetch(`/api/payments/${paymentToDelete.id}`, {
        method: 'DELETE'
      })

      if (!response.ok) {
        const error = await response.json()
        throw new Error(error.error || '删除失败')
      }

      showSuccessToast('删除成功', `付款记录已删除，金额：${formatCurrency(paymentToDelete.amount)}`)
      fetchPayments()
      setDeleteDialogOpen(false)
      setPaymentToDelete(null)
    } catch (error) {
      console.error('Error deleting payment:', error)
      showErrorToast('删除失败', error instanceof Error ? error.message : '删除付款记录失败')
    } finally {
      setIsDeleting(false)
    }
  }

  const filteredPayments = payments ? payments.filter(payment => {
    const matchesSearch =
      payment.invoices.invoice_number.toLowerCase().includes(searchTerm.toLowerCase()) ||
      `${payment.clients.last_name}${payment.clients.first_name}`.includes(searchTerm) ||
      payment.clients.phone.includes(searchTerm) ||
      (payment.reference_number && payment.reference_number.toLowerCase().includes(searchTerm.toLowerCase()))

    const matchesMethod = methodFilter === 'all' || payment.payment_method === methodFilter
    const matchesType = typeFilter === 'all' || payment.payment_type === typeFilter

    return matchesSearch && matchesMethod && matchesType
  }) : []

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(amount)
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        timeZone: 'Asia/Shanghai'
      })
  }

  const getStats = () => {
    const total = payments.length
    const totalAmount = payments.reduce((sum, payment) => sum + payment.amount, 0)
    const deposits = payments.filter(p => p.payment_type === 'deposit').length
    const fullPayments = payments.filter(p => p.payment_type === 'full_payment').length
    
    return { total, totalAmount, deposits, fullPayments }
  }

  const stats = getStats()

  if (loading) {
    return (
      <PageContainer>
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <IconClock className="mx-auto h-8 w-8 text-gray-400 animate-spin" />
            <p className="mt-2 text-gray-500">加载中...</p>
          </div>
        </div>
      </PageContainer>
    )
  }

  return (
    <PageContainer>
      <div className='flex flex-1 flex-col space-y-4'>
        {/* Header */}
        <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between gap-4">
          <div>
            <h1 className="text-2xl sm:text-3xl font-bold tracking-tight">付款记录</h1>
            <p className="text-muted-foreground">查看和管理所有付款记录</p>
          </div>
          <Button onClick={handleNewPayment} className="w-full sm:w-auto">
            <IconPlus className="mr-2 h-4 w-4" />
            记录付款
          </Button>
        </div>

        {/* Stats Cards */}
        <div className="grid gap-4 md:grid-cols-4">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">总付款数</CardTitle>
              <IconCreditCard className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats.total}</div>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">总金额</CardTitle>
              <IconCreditCard className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{formatCurrency(stats.totalAmount)}</div>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">定金付款</CardTitle>
              <IconCash className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats.deposits}</div>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">全额付款</CardTitle>
              <IconBuildingBank className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats.fullPayments}</div>
            </CardContent>
          </Card>
        </div>

        {/* Filters and Table */}
        <Card>
          <CardHeader>
            <CardTitle>付款记录</CardTitle>
            <CardDescription>查看所有客户付款记录和详情</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="flex flex-col sm:flex-row gap-4 mb-4">
              <div className="relative flex-1">
                <IconSearch className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                <Input
                  placeholder="搜索账单号、客户姓名、电话或参考号..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
              <div className="flex gap-2">
                <Select value={methodFilter} onValueChange={setMethodFilter}>
                  <SelectTrigger className="w-full sm:w-40">
                    <SelectValue placeholder="付款方式" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">全部方式</SelectItem>
                    <SelectItem value="cash">现金</SelectItem>
                    <SelectItem value="credit_card">信用卡</SelectItem>
                    <SelectItem value="debit_card">借记卡</SelectItem>
                    <SelectItem value="bank_transfer">银行转账</SelectItem>
                    <SelectItem value="check">支票</SelectItem>
                    <SelectItem value="other">其他</SelectItem>
                  </SelectContent>
                </Select>
                <Select value={typeFilter} onValueChange={setTypeFilter}>
                  <SelectTrigger className="w-full sm:w-40">
                  <SelectValue placeholder="付款类型" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">全部类型</SelectItem>
                  <SelectItem value="deposit">定金</SelectItem>
                  <SelectItem value="partial_payment">部分付款</SelectItem>
                  <SelectItem value="full_payment">全额付款</SelectItem>
                </SelectContent>
              </Select>
              </div>
            </div>

            {/* Table */}
            <ResponsiveTable
              data={filteredPayments}
              desktopTable={
                <div className="rounded-md border">
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>账单号</TableHead>
                        <TableHead>客户</TableHead>
                        <TableHead>付款金额</TableHead>
                        <TableHead>付款日期</TableHead>
                        <TableHead>付款方式</TableHead>
                        <TableHead>付款类型</TableHead>
                        <TableHead>参考号</TableHead>
                        <TableHead className="text-right">操作</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {filteredPayments.map((payment) => {
                        const methodInfo = paymentMethodConfig[payment.payment_method]
                        const typeInfo = paymentTypeConfig[payment.payment_type]
                        const MethodIcon = methodInfo.icon

                        return (
                          <TableRow key={payment.id}>
                            <TableCell className="font-medium">
                              {payment.invoices.invoice_number}
                            </TableCell>
                            <TableCell>
                              <div>
                                <div className="font-medium">
                                  {payment.clients.last_name}{payment.clients.first_name}
                                </div>
                                <div className="text-sm text-muted-foreground">
                                  {payment.clients.phone}
                                </div>
                              </div>
                            </TableCell>
                            <TableCell className="font-medium">
                              {formatCurrency(payment.amount)}
                            </TableCell>
                            <TableCell>{formatDate(payment.payment_date)}</TableCell>
                            <TableCell>
                              <div className="flex items-center gap-2">
                                <MethodIcon className="h-4 w-4" />
                                {methodInfo.label}
                              </div>
                            </TableCell>
                            <TableCell>
                              <Badge className={typeInfo.color}>
                                {typeInfo.label}
                              </Badge>
                            </TableCell>
                            <TableCell>
                              {payment.reference_number || '-'}
                            </TableCell>
                            <TableCell className="text-right">
                              <div className="flex items-center justify-end gap-2">
                                <Button
                                  variant="ghost"
                                  size="sm"
                                  onClick={() => handleEditPayment(payment)}
                                  className="h-8 w-8 p-0"
                                >
                                  <IconEdit className="h-4 w-4" />
                                </Button>
                                <Button
                                  variant="ghost"
                                  size="sm"
                                  onClick={() => handleDeletePayment(payment)}
                                  className="h-8 w-8 p-0 text-red-600 hover:text-red-700 hover:bg-red-50"
                                >
                                  <IconTrash className="h-4 w-4" />
                                </Button>
                              </div>
                            </TableCell>
                          </TableRow>
                        )
                      })}
                    </TableBody>
                  </Table>
                </div>
              }
              renderMobileCard={(payment) => {
                const methodInfo = paymentMethodConfig[payment.payment_method as keyof typeof paymentMethodConfig]
                const typeInfo = paymentTypeConfig[payment.payment_type as keyof typeof paymentTypeConfig]
                const MethodIcon = methodInfo.icon

                return (
                  <div className="space-y-3">
                    <div className="flex items-center justify-between">
                      <h3 className="font-medium text-lg">{payment.invoices.invoice_number}</h3>
                      <Badge className={typeInfo.color}>
                        {typeInfo.label}
                      </Badge>
                    </div>

                    <div className="space-y-2">
                      <MobileTableField
                        label="客户"
                        value={
                          <div>
                            <div className="font-medium">
                              {payment.clients.last_name}{payment.clients.first_name}
                            </div>
                            <div className="text-sm text-muted-foreground">
                              {payment.clients.phone}
                            </div>
                          </div>
                        }
                      />

                      <MobileTableField
                        label="付款金额"
                        value={<span className="font-medium text-green-600">{formatCurrency(payment.amount)}</span>}
                      />

                      <MobileTableField
                        label="付款日期"
                        value={formatDate(payment.payment_date)}
                      />

                      <MobileTableField
                        label="付款方式"
                        value={
                          <div className="flex items-center gap-2">
                            <MethodIcon className="h-4 w-4" />
                            {methodInfo.label}
                          </div>
                        }
                      />

                      <MobileTableField
                        label="参考号"
                        value={payment.reference_number || '-'}
                      />

                      <MobileTableField
                        label="操作"
                        value={
                          <div className="flex items-center gap-2">
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => handleEditPayment(payment)}
                              className="h-8"
                            >
                              <IconEdit className="h-4 w-4 mr-1" />
                              编辑
                            </Button>
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => handleDeletePayment(payment)}
                              className="h-8 text-red-600 hover:text-red-700 hover:bg-red-50"
                            >
                              <IconTrash className="h-4 w-4 mr-1" />
                              删除
                            </Button>
                          </div>
                        }
                      />
                    </div>
                  </div>
                )
              }}
            />

            {filteredPayments.length === 0 && (
              <div className="text-center py-8">
                <IconCreditCard className="mx-auto h-12 w-12 text-gray-400" />
                <h3 className="mt-2 text-sm font-semibold text-gray-900">暂无付款记录</h3>
                <p className="mt-1 text-sm text-gray-500">
                  {searchTerm || methodFilter !== 'all' || typeFilter !== 'all' 
                    ? '没有找到匹配的付款记录' 
                    : '开始记录第一笔付款'}
                </p>
              </div>
            )}
          </CardContent>
        </Card>
      </div>

      {/* Payment Modal */}
      <PaymentModal
        isOpen={isPaymentModalOpen}
        onClose={() => setIsPaymentModalOpen(false)}
        onSuccess={handlePaymentSuccess}
        editingPayment={editingPayment}
      />

      {/* Delete Confirmation Dialog */}
      <AlertDialog open={deleteDialogOpen} onOpenChange={setDeleteDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>确认删除付款记录</AlertDialogTitle>
            <AlertDialogDescription>
              您确定要删除这笔付款记录吗？
              <br />
              <strong>金额：{paymentToDelete && formatCurrency(paymentToDelete.amount)}</strong>
              <br />
              <strong>账单号：{paymentToDelete?.invoices.invoice_number}</strong>
              <br />
              <br />
              此操作将永久删除该付款记录，无法撤销。
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel disabled={isDeleting}>取消</AlertDialogCancel>
            <AlertDialogAction
              onClick={confirmDeletePayment}
              disabled={isDeleting}
              className="bg-red-600 hover:bg-red-700"
            >
              {isDeleting ? '删除中...' : '确认删除'}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </PageContainer>
  )
}
