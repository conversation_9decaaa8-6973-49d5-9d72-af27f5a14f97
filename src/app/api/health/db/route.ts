import { NextRequest, NextResponse } from 'next/server'
import { supabase } from '@/lib/supabase/client'

/**
 * Database Health Check Endpoint
 * Specific endpoint for database connectivity testing
 */

interface DatabaseHealth {
  status: 'healthy' | 'unhealthy'
  timestamp: string
  responseTime: number
  connection: {
    url: string
    authenticated: boolean
  }
  tables: {
    accessible: string[]
    errors: string[]
  }
  error?: string
}

export async function GET(request: NextRequest) {
  const start = Date.now()
  
  try {
    const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL
    
    // Test basic connectivity
    const { data: authData, error: authError } = await supabase.auth.getSession()
    
    // Test table access
    const tableTests = [
      'clients',
      'treatments', 
      'appointments',
      'invoices',
      'payments'
    ]
    
    const accessible: string[] = []
    const errors: string[] = []
    
    for (const table of tableTests) {
      try {
        const { error } = await supabase
          .from(table)
          .select('id')
          .limit(1)
        
        if (error) {
          errors.push(`${table}: ${error.message}`)
        } else {
          accessible.push(table)
        }
      } catch (err) {
        errors.push(`${table}: ${err instanceof Error ? err.message : 'Unknown error'}`)
      }
    }
    
    const responseTime = Date.now() - start
    const isHealthy = errors.length === 0
    
    const health: DatabaseHealth = {
      status: isHealthy ? 'healthy' : 'unhealthy',
      timestamp: new Date().toISOString(),
      responseTime,
      connection: {
        url: supabaseUrl ? supabaseUrl.replace(/\/\/.*@/, '//***@') : 'not configured',
        authenticated: !authError
      },
      tables: {
        accessible,
        errors
      }
    }
    
    if (!isHealthy) {
      health.error = `Failed to access ${errors.length} tables`
    }
    
    return NextResponse.json(health, { 
      status: isHealthy ? 200 : 503 
    })
    
  } catch (error) {
    const responseTime = Date.now() - start
    
    const health: DatabaseHealth = {
      status: 'unhealthy',
      timestamp: new Date().toISOString(),
      responseTime,
      connection: {
        url: process.env.NEXT_PUBLIC_SUPABASE_URL || 'not configured',
        authenticated: false
      },
      tables: {
        accessible: [],
        errors: ['Connection failed']
      },
      error: error instanceof Error ? error.message : 'Database connection failed'
    }
    
    return NextResponse.json(health, { status: 503 })
  }
}
