import type { NextConfig } from 'next';
import { withSentryConfig } from '@sentry/nextjs';

// Define the base Next.js configuration
const baseConfig: NextConfig = {
  // Enable React strict mode for better development experience
  reactStrictMode: true,

  // External packages for server components
  serverExternalPackages: ['@prisma/client', 'bcryptjs'],

  // Experimental features for performance (Next.js 15 compatible)
  experimental: {
    // Temporarily disable CSS optimization due to critters dependency issue
    optimizeCss: false,

    // Enable modern JavaScript features
    esmExternals: true,
  },

  // Image optimization configuration
  images: {
    // Enable image optimization
    unoptimized: false,

    // Supported formats
    formats: ['image/webp', 'image/avif'],

    // Device sizes for responsive images
    deviceSizes: [640, 750, 828, 1080, 1200, 1920, 2048, 3840],

    // Image sizes for different breakpoints
    imageSizes: [16, 32, 48, 64, 96, 128, 256, 384],

    // Remote patterns for more flexible image sources
    remotePatterns: [
      {
        protocol: 'https',
        hostname: 'api.slingacademy.com',
        port: ''
      },
      {
        protocol: 'https',
        hostname: '**.supabase.co',
        port: '',
        pathname: '/storage/v1/object/public/**',
      },
    ],

    // Minimize layout shift
    minimumCacheTTL: 60 * 60 * 24 * 30, // 30 days
  },

  // Transpile packages
  transpilePackages: ['geist'],

  // Webpack configuration for performance optimization
  webpack: (config, { buildId, dev, isServer, defaultLoaders, webpack }) => {
    // Bundle analyzer (only when ANALYZE=true)
    if (process.env.ANALYZE === 'true') {
      try {
        const BundleAnalyzerPlugin = require('webpack-bundle-analyzer').BundleAnalyzerPlugin
        config.plugins.push(
          new BundleAnalyzerPlugin({
            analyzerMode: 'static',
            openAnalyzer: false,
            reportFilename: isServer ? '../analyze/server.html' : './analyze/client.html',
          })
        )
      } catch (error) {
        console.warn('webpack-bundle-analyzer not installed, skipping bundle analysis')
      }
    }

    // Optimize bundle splitting (more conservative approach)
    if (!isServer) {
      config.optimization.splitChunks = {
        chunks: 'async',
        maxSize: 244000, // 244KB max chunk size
        cacheGroups: {
          // Framework chunk for React and Next.js
          framework: {
            test: /[\\/]node_modules[\\/](react|react-dom|next)[\\/]/,
            name: 'framework',
            priority: 40,
            enforce: true,
            reuseExistingChunk: true,
          },
          // UI library chunk
          ui: {
            test: /[\\/]node_modules[\\/](@radix-ui|@headlessui|framer-motion|lucide-react)[\\/]/,
            name: 'ui',
            priority: 30,
            reuseExistingChunk: true,
          },
          // Charts and data visualization
          charts: {
            test: /[\\/]node_modules[\\/](recharts|d3-)[\\/]/,
            name: 'charts',
            priority: 25,
            reuseExistingChunk: true,
          },
          // Common vendor libraries
          vendor: {
            test: /[\\/]node_modules[\\/]/,
            name: 'vendor',
            priority: 10,
            minChunks: 2,
            reuseExistingChunk: true,
          },
        },
      }
    }

    // Tree shaking optimization
    config.optimization.usedExports = true
    config.optimization.sideEffects = false

    // Resolve aliases for cleaner imports
    config.resolve.alias = {
      ...config.resolve.alias,
      '@': require('path').resolve(__dirname, 'src'),
      '~': require('path').resolve(__dirname, 'public'),
    }

    // Production optimizations
    if (!dev) {
      // Enable gzip compression
      try {
        const CompressionPlugin = require('compression-webpack-plugin')
        config.plugins.push(
          new CompressionPlugin({
            algorithm: 'gzip',
            test: /\.(js|css|html|svg)$/,
            threshold: 8192,
            minRatio: 0.8,
          })
        )
      } catch (error) {
        console.warn('compression-webpack-plugin not installed, skipping gzip compression')
      }
    }

    return config
  },

  // Headers for security and performance
  async headers() {
    return [
      {
        source: '/(.*)',
        headers: [
          // Security headers
          {
            key: 'X-DNS-Prefetch-Control',
            value: 'on',
          },
          {
            key: 'X-XSS-Protection',
            value: '1; mode=block',
          },
          {
            key: 'X-Frame-Options',
            value: 'SAMEORIGIN',
          },
          {
            key: 'X-Content-Type-Options',
            value: 'nosniff',
          },
          {
            key: 'Referrer-Policy',
            value: 'origin-when-cross-origin',
          },
        ],
      },
    ]
  },

  // Compression
  compress: true,

  // Power by header
  poweredByHeader: false,

  // Generate ETags for better caching
  generateEtags: true,

  // HTTP keep alive
  httpAgentOptions: {
    keepAlive: true,
  },

  // Output configuration
  output: 'standalone', // For Docker deployment

  // TypeScript configuration
  typescript: {
    // Temporarily disable type checking for production build
    ignoreBuildErrors: true,
  },

  // ESLint configuration
  eslint: {
    // Don't fail build on ESLint warnings in production
    ignoreDuringBuilds: true,
  },

  // Trailing slash configuration
  trailingSlash: false,

  // Page extensions
  pageExtensions: ['ts', 'tsx', 'js', 'jsx'],
};

let configWithPlugins = baseConfig;

// Conditionally enable Sentry configuration only if org and project are provided
if (!process.env.NEXT_PUBLIC_SENTRY_DISABLED &&
    process.env.NEXT_PUBLIC_SENTRY_ORG &&
    process.env.NEXT_PUBLIC_SENTRY_PROJECT) {
  configWithPlugins = withSentryConfig(configWithPlugins, {
    // For all available options, see:
    // https://www.npmjs.com/package/@sentry/webpack-plugin#options
    org: process.env.NEXT_PUBLIC_SENTRY_ORG,
    project: process.env.NEXT_PUBLIC_SENTRY_PROJECT,
    // Only print logs for uploading source maps in CI
    silent: !process.env.CI,

    // Upload a larger set of source maps for prettier stack traces (increases build time)
    widenClientFileUpload: true,

    // Upload a larger set of source maps for prettier stack traces (increases build time)
    reactComponentAnnotation: {
      enabled: true
    },

    // Route browser requests to Sentry through a Next.js rewrite to circumvent ad-blockers.
    tunnelRoute: '/monitoring',

    // Automatically tree-shake Sentry logger statements to reduce bundle size
    disableLogger: true,

    // Disable Sentry telemetry
    telemetry: false
  });
}

const nextConfig = configWithPlugins;
export default nextConfig;
