# Production Readiness Test Report

Generated on: 2025-07-22T17:03:08.532Z

## Overall Status: READY_WITH_WARNINGS

⚠️ **READY WITH WARNINGS** - Critical tests passed, but some warnings need attention

## Test Results Summary

- **Critical Failures**: 0
- **Warnings**: 2
- **Total Tests**: 23

## Detailed Results


### Install dependencies
- **Status**: ✅ PASSED
- **Required**: Yes
- **Command**: `pnpm install --frozen-lockfile`



### Production build
- **Status**: ✅ PASSED
- **Required**: Yes
- **Command**: `pnpm build`



### Code quality check
- **Status**: ⚠️ WARNING
- **Required**: No
- **Command**: `pnpm lint --max-warnings 50`
- **Error**: Command failed: pnpm lint --max-warnings 50
 ⚠ Warning: Found multiple lockfiles. Selecting /Users/<USER>/package-lock.json.
   Consider removing the lockfiles at:
   * /Users/<USER>/Desktop/nord_new/pnpm-lock.yaml


./src/app/api/admin/performance/route.ts
22:11  Warning: 'cacheMetrics' is assigned a value but never used.  @typescript-eslint/no-unused-vars
146:53  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
168:8  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
169:11  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
215:15  Warning: 'request' is defined but never used.  @typescript-eslint/no-unused-vars
215:24  Warning: 'context' is defined but never used.  @typescript-eslint/no-unused-vars
246:28  Warning: 'request' is defined but never used.  @typescript-eslint/no-unused-vars
272:31  Warning: 'request' is defined but never used.  @typescript-eslint/no-unused-vars

./src/app/api/appointments/[id]/route.ts
14:5  Warning: Unexpected console statement.  no-console
55:23  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
79:11  Warning: Unexpected console statement.  no-console
86:11  Warning: Unexpected console statement.  no-console
93:5  Warning: Unexpected console statement.  no-console
111:5  Warning: Unexpected console statement.  no-console

./src/app/api/appointments/conflicts/route.ts
25:5  Warning: Unexpected console statement.  no-console

./src/app/api/appointments/route.ts
27:5  Warning: Unexpected console statement.  no-console
79:9  Warning: Unexpected console statement.  no-console
89:9  Warning: Unexpected console statement.  no-console
96:5  Warning: Unexpected console statement.  no-console

./src/app/api/clients/[id]/route.ts
14:5  Warning: Unexpected console statement.  no-console
54:19  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
55:5  Warning: Unexpected console statement.  no-console
82:5  Warning: Unexpected console statement.  no-console

./src/app/api/clients/route.ts
1:10  Warning: 'NextRequest' is defined but never used.  @typescript-eslint/no-unused-vars
1:23  Warning: 'NextResponse' is defined but never used.  @typescript-eslint/no-unused-vars
3:51  Warning: 'apiErrors' is defined but never used.  @typescript-eslint/no-unused-vars
4:10  Warning: 'clientSchemas' is defined but never used.  @typescript-eslint/no-unused-vars
4:25  Warning: 'commonSchemas' is defined but never used.  @typescript-eslint/no-unused-vars
42:47  Warning: Prefer using nullish coalescing operator (`??`) instead of a logical or (`||`), as it is a safer operator.  @typescript-eslint/prefer-nullish-coalescing
68:24  Warning: Forbidden non-null assertion.  @typescript-eslint/no-non-null-assertion
74:31  Warning: Prefer using nullish coalescing operator (`??`) instead of a logical or (`||`), as it is a safer operator.  @typescript-eslint/prefer-nullish-coalescing
75:47  Warning: Prefer using nullish coalescing operator (`??`) instead of a logical or (`||`), as it is a safer operator.  @typescript-eslint/prefer-nullish-coalescing
76:49  Warning: Prefer using nullish coalescing operator (`??`) instead of a logical or (`||`), as it is a safer operator.  @typescript-eslint/prefer-nullish-coalescing
77:49  Warning: Prefer using nullish coalescing operator (`??`) instead of a logical or (`||`), as it is a safer operator.  @typescript-eslint/prefer-nullish-coalescing
78:29  Warning: Prefer using nullish coalescing operator (`??`) instead of a logical or (`||`), as it is a safer operator.  @typescript-eslint/prefer-nullish-coalescing
79:49  Warning: Prefer using nullish coalescing operator (`??`) instead of a logical or (`||`), as it is a safer operator.  @typescript-eslint/prefer-nullish-coalescing
80:43  Warning: Prefer using nullish coalescing operator (`??`) instead of a logical or (`||`), as it is a safer operator.  @typescript-eslint/prefer-nullish-coalescing
81:35  Warning: Prefer using nullish coalescing operator (`??`) instead of a logical or (`||`), as it is a safer operator.  @typescript-eslint/prefer-nullish-coalescing
82:37  Warning: Prefer using nullish coalescing operator (`??`) instead of a logical or (`||`), as it is a safer operator.  @typescript-eslint/prefer-nullish-coalescing
83:39  Warning: Prefer using nullish coalescing operator (`??`) instead of a logical or (`||`), as it is a safer operator.  @typescript-eslint/prefer-nullish-coalescing
84:65  Warning: Prefer using nullish coalescing operator (`??`) instead of a logical or (`||`), as it is a safer operator.  @typescript-eslint/prefer-nullish-coalescing
85:67  Warning: Prefer using nullish coalescing operator (`??`) instead of a logical or (`||`), as it is a safer operator.  @typescript-eslint/prefer-nullish-coalescing
86:31  Warning: Prefer using nullish coalescing operator (`??`) instead of a logical or (`||`), as it is a safer operator.  @typescript-eslint/prefer-nullish-coalescing
87:51  Warning: Prefer using nullish coalescing operator (`??`) instead of a logical or (`||`), as it is a safer operator.  @typescript-eslint/prefer-nullish-coalescing
88:57  Warning: Prefer using nullish coalescing operator (`??`) instead of a logical or (`||`), as it is a safer operator.  @typescript-eslint/prefer-nullish-coalescing

./src/app/api/health/db/route.ts
24:27  Warning: 'request' is defined but never used.  @typescript-eslint/no-unused-vars
31:19  Warning: 'authData' is assigned a value but never used.  @typescript-eslint/no-unused-vars
95:51  Warning: Prefer using nullish coalescing operator (`??`) instead of a logical or (`||`), as it is a safer operator.  @typescript-eslint/prefer-nullish-coalescing

./src/app/api/health/route.ts
41:13  Warning: 'data' is assigned a value but never used.  @typescript-eslint/no-unused-vars
96:27  Warning: 'request' is defined but never used.  @typescript-eslint/no-unused-vars
119:48  Warning: Prefer using nullish coalescing operator (`??`) instead of a logical or (`||`), as it is a safer operator.  @typescript-eslint/prefer-nullish-coalescing
138:48  Warning: Prefer using nullish coalescing operator (`??`) instead of a logical or (`||`), as it is a safer operator.  @typescript-eslint/prefer-nullish-coalescing
163:28  Warning: 'request' is defined but never used.  @typescript-eslint/no-unused-vars

./src/app/api/invoices/[id]/route.ts
14:5  Warning: Unexpected console statement.  no-console
32:23  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
57:5  Warning: Unexpected console statement.  no-console
75:5  Warning: Unexpected console statement.  no-console

./src/app/api/invoices/route.ts
19:5  Warning: Unexpected console statement.  no-console
76:5  Warning: Unexpected console statement.  no-console

./src/app/api/payments/[id]/route.ts
14:5  Warning: Unexpected console statement.  no-console
35:23  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
53:5  Warning: Unexpected console statement.  no-console
79:5  Warning: Unexpected console statement.  no-console

./src/app/api/payments/route.ts
22:5  Warning: Unexpected console statement.  no-console
61:5  Warning: Unexpected console statement.  no-console

./src/app/api/treatments/[id]/route.ts
14:5  Warning: Unexpected console statement.  no-console
31:23  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
59:5  Warning: Unexpected console statement.  no-console
77:5  Warning: Unexpected console statement.  no-console

./src/app/api/treatments/route.ts
19:5  Warning: Unexpected console statement.  no-console
56:5  Warning: Unexpected console statement.  no-console

./src/app/dashboard/calendar/page.tsx
58:64  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
102:78  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
119:7  Warning: Unexpected console statement.  no-console
127:6  Warning: React Hook useEffect has a missing dependency: 'fetchAppointments'. Either include it or remove the dependency array.  react-hooks/exhaustive-deps

./src/app/dashboard/clients/[id]/page.tsx
29:10  Warning: 'showSuccessToast' is defined but never used.  @typescript-eslint/no-unused-vars
116:21  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
117:7  Warning: Unexpected console statement.  no-console
129:6  Warning: React Hook useEffect has a missing dependency: 'fetchClientData'. Either include it or remove the dependency array.  react-hooks/exhaustive-deps
307:45  Warning: Prefer using nullish coalescing operator (`??`) instead of a logical or (`||`), as it is a safer operator.  @typescript-eslint/prefer-nullish-coalescing
331:48  Warning: Prefer using nullish coalescing operator (`??`) instead of a logical or (`||`), as it is a safer operator.  @typescript-eslint/prefer-nullish-coalescing

./src/app/dashboard/clients/page.tsx
27:25  Warning: 'StatsCardSkeleton' is defined but never used.  @typescript-eslint/no-unused-vars
81:7  Warning: Unexpected console statement.  no-console
270:53  Warning: Prefer using nullish coalescing operator (`??`) instead of a logical or (`||`), as it is a safer operator.  @typescript-eslint/prefer-nullish-coalescing

./src/app/dashboard/invoices/page.tsx
59:56  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
74:7  Warning: Unexpected console statement.  no-console
118:7  Warning: Unexpected console statement.  no-console

./src/app/dashboard/payments/page.tsx
63:56  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
78:7  Warning: Unexpected console statement.  no-console
122:7  Warning: Unexpected console statement.  no-console
333:57  Warning: Prefer using nullish coalescing operator (`??`) instead of a logical or (`||`), as it is a safer operator.  @typescript-eslint/prefer-nullish-coalescing

./src/app/dashboard/product/page.tsx
3:10  Warning: 'buttonVariants' is defined but never used.  @typescript-eslint/no-unused-vars
4:10  Warning: 'Heading' is defined but never used.  @typescript-eslint/no-unused-vars
5:10  Warning: 'Separator' is defined but never used.  @typescript-eslint/no-unused-vars
8:29  Warning: 'serialize' is defined but never used.  @typescript-eslint/no-unused-vars
9:10  Warning: 'cn' is defined but never used.  @typescript-eslint/no-unused-vars
11:8  Warning: 'Link' is defined but never used.  @typescript-eslint/no-unused-vars

./src/app/dashboard/settings/appointment-types/page.tsx
94:7  Warning: Unexpected console statement.  no-console
103:7  Warning: Unexpected console statement.  no-console
175:37  Warning: Prefer using nullish coalescing operator (`??`) instead of a logical or (`||`), as it is a safer operator.  @typescript-eslint/prefer-nullish-coalescing
317:39  Warning: Prefer using nullish coalescing operator (`??`) instead of a logical or (`||`), as it is a safer operator.  @typescript-eslint/prefer-nullish-coalescing

./src/app/dashboard/settings/page.tsx
12:10  Warning: 'EnhancedInput' is defined but never used.  @typescript-eslint/no-unused-vars
12:25  Warning: 'EnhancedTextarea' is defined but never used.  @typescript-eslint/no-unused-vars
12:43  Warning: 'EnhancedSelect' is defined but never used.  @typescript-eslint/no-unused-vars
18:3  Warning: 'Database' is defined but never used.  @typescript-eslint/no-unused-vars
19:3  Warning: 'Palette' is defined but never used.  @typescript-eslint/no-unused-vars
22:3  Warning: 'Mail' is defined but never used.  @typescript-eslint/no-unused-vars
23:3  Warning: 'Phone' is defined but never used.  @typescript-eslint/no-unused-vars
24:3  Warning: 'MapPin' is defined but never used.  @typescript-eslint/no-unused-vars
138:7  Warning: Unexpected console statement.  no-console
162:21  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
163:7  Warning: Unexpected console statement.  no-console
175:47  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
179:20  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any

./src/app/dashboard/settings/treatment-categories/page.tsx
99:7  Warning: Unexpected console statement.  no-console
109:7  Warning: Unexpected console statement.  no-console
170:41  Warning: Prefer using nullish coalescing operator (`??`) instead of a logical or (`||`), as it is a safer operator.  @typescript-eslint/prefer-nullish-coalescing
171:29  Warning: Prefer using nullish coalescing operator (`??`) instead of a logical or (`||`), as it is a safer operator.  @typescript-eslint/prefer-nullish-coalescing
301:43  Warning: Prefer using nullish coalescing operator (`??`) instead of a logical or (`||`), as it is a safer operator.  @typescript-eslint/prefer-nullish-coalescing

./src/app/dashboard/settings/working-hours/page.tsx
201:44  Warning: Prefer using nullish coalescing operator (`??`) instead of a logical or (`||`), as it is a safer operator.  @typescript-eslint/prefer-nullish-coalescing
213:42  Warning: Prefer using nullish coalescing operator (`??`) instead of a logical or (`||`), as it is a safer operator.  @typescript-eslint/prefer-nullish-coalescing

./src/app/dashboard/treatments/page.tsx
58:7  Warning: Unexpected console statement.  no-console
123:5  Warning: Forbidden non-null assertion.  @typescript-eslint/no-non-null-assertion
167:48  Warning: Prefer using nullish coalescing operator (`??`) instead of a logical or (`||`), as it is a safer operator.  @typescript-eslint/prefer-nullish-coalescing

./src/components/active-theme.tsx
35:24  Warning: Prefer using nullish coalescing operator (`??`) instead of a logical or (`||`), as it is a safer operator.  @typescript-eslint/prefer-nullish-coalescing

./src/components/admin/PerformanceDashboard.tsx
20:3  Warning: 'Server' is defined but never used.  @typescript-eslint/no-unused-vars
91:7  Warning: Unexpected console statement.  no-console
237:42  Warning: Prefer using nullish coalescing operator (`??`) instead of a logical or (`||`), as it is a safer operator.  @typescript-eslint/prefer-nullish-coalescing
253:60  Warning: Prefer using nullish coalescing operator (`??`) instead of a logical or (`||`), as it is a safer operator.  @typescript-eslint/prefer-nullish-coalescing
278:35  Warning: Forbidden non-null assertion.  @typescript-eslint/no-non-null-assertion
284:35  Warning: Forbidden non-null assertion.  @typescript-eslint/no-non-null-assertion
336:71  Warning: Prefer using nullish coalescing operator (`??`) instead of a logical or (`||`), as it is a safer operator.  @typescript-eslint/prefer-nullish-coalescing
340:74  Warning: Prefer using nullish coalescing operator (`??`) instead of a logical or (`||`), as it is a safer operator.  @typescript-eslint/prefer-nullish-coalescing
361:35  Warning: Forbidden non-null assertion.  @typescript-eslint/no-non-null-assertion
367:35  Warning: Forbidden non-null assertion.  @typescript-eslint/no-non-null-assertion
388:50  Warning: Prefer using nullish coalescing operator (`??`) instead of a logical or (`||`), as it is a safer operator.  @typescript-eslint/prefer-nullish-coalescing
392:53  Warning: Prefer using nullish coalescing operator (`??`) instead of a logical or (`||`), as it is a safer operator.  @typescript-eslint/prefer-nullish-coalescing
452:51  Warning: Prefer using nullish coalescing operator (`??`) instead of a logical or (`||`), as it is a safer operator.  @typescript-eslint/prefer-nullish-coalescing
456:54  Warning: Prefer using nullish coalescing operator (`??`) instead of a logical or (`||`), as it is a safer operator.  @typescript-eslint/prefer-nullish-coalescing
464:54  Warning: Prefer using nullish coalescing operator (`??`) instead of a logical or (`||`), as it is a safer operator.  @typescript-eslint/prefer-nullish-coalescing
468:57  Warning: Prefer using nullish coalescing operator (`??`) instead of a logical or (`||`), as it is a safer operator.  @typescript-eslint/prefer-nullish-coalescing
484:58  Warning: Prefer using nullish coalescing operator (`??`) instead of a logical or (`||`), as it is a safer operator.  @typescript-eslint/prefer-nullish-coalescing

./src/components/admin/SecurityDashboard.tsx
106:7  Warning: Unexpected console statement.  no-console
220:7  Warning: Unexpected console statement.  no-console
239:7  Warning: Unexpected console statement.  no-console
320:67  Warning: Prefer using nullish coalescing operator (`??`) instead of a logical or (`||`), as it is a safer operator.  @typescript-eslint/prefer-nullish-coalescing
324:69  Warning: Prefer using nullish coalescing operator (`??`) instead of a logical or (`||`), as it is a safer operator.  @typescript-eslint/prefer-nullish-coalescing
445:71  Warning: Prefer using nullish coalescing operator (`??`) instead of a logical or (`||`), as it is a safer operator.  @typescript-eslint/prefer-nullish-coalescing
446:111  Warning: Prefer using nullish coalescing operator (`??`) instead of a logical or (`||`), as it is a safer operator.  @typescript-eslint/prefer-nullish-coalescing
469:70  Warning: Prefer using nullish coalescing operator (`??`) instead of a logical or (`||`), as it is a safer operator.  @typescript-eslint/prefer-nullish-coalescing
470:110  Warning: Prefer using nullish coalescing operator (`??`) instead of a logical or (`||`), as it is a safer operator.  @typescript-eslint/prefer-nullish-coalescing
495:35  Warning: Forbidden non-null assertion.  @typescript-eslint/no-non-null-assertion
501:35  Warning: Forbidden non-null assertion.  @typescript-eslint/no-non-null-assertion
553:35  Warning: Forbidden non-null assertion.  @typescript-eslint/no-non-null-assertion

./src/components/analytics/AdvancedAnalyticsDashboard.tsx
24:3  Warning: 'BarChart3' is defined but never used.  @typescript-eslint/no-unused-vars
25:3  Warning: 'PieChart' is defined but never used.  @typescript-eslint/no-unused-vars
26:3  Warning: 'LineChart' is defined but never used.  @typescript-eslint/no-unused-vars
29:3  Warning: 'Filter' is defined but never used.  @typescript-eslint/no-unused-vars
55:20  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
56:24  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
71:10  Warning: 'selectedTimeRange' is assigned a value but never used.  @typescript-eslint/no-unused-vars
71:29  Warning: 'setSelectedTimeRange' is assigned a value but never used.  @typescript-eslint/no-unused-vars
92:7  Warning: Unexpected console statement.  no-console
378:86  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
406:80  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
442:83  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any

./src/components/analytics/BusinessAnalyticsDashboard.tsx
23:3  Warning: 'LineChart' is defined but never used.  @typescript-eslint/no-unused-vars
24:3  Warning: 'Line' is defined but never used.  @typescript-eslint/no-unused-vars
33:3  Warning: 'TrendingDown' is defined but never used.  @typescript-eslint/no-unused-vars
37:3  Warning: 'Package' is defined but never used.  @typescript-eslint/no-unused-vars
38:3  Warning: 'Clock' is defined but never used.  @typescript-eslint/no-unused-vars
42:3  Warning: 'BarChart3' is defined but never used.  @typescript-eslint/no-unused-vars
45:27  Warning: 'startOfMonth' is defined but never used.  @typescript-eslint/no-unused-vars
45:41  Warning: 'endOfMonth' is defined but never used.  @typescript-eslint/no-unused-vars
46:10  Warning: 'zhCN' is defined but never used.  @typescript-eslint/no-unused-vars
90:7  Warning: 'COLORS' is assigned a value but never used.  @typescript-eslint/no-unused-vars
155:7  Warning: Unexpected console statement.  no-console
167:5  Warning: Unexpected console statement.  no-console

./src/components/appointment/AppointmentConflictChecker.tsx
6:10  Warning: 'Button' is defined but never used.  @typescript-eslint/no-unused-vars
8:10  Warning: 'format' is defined but never used.  @typescript-eslint/no-unused-vars
9:10  Warning: 'zhCN' is defined but never used.  @typescript-eslint/no-unused-vars
46:89  Warning: Prefer using nullish coalescing operator (`??`) instead of a logical or (`||`), as it is a safer operator.  @typescript-eslint/prefer-nullish-coalescing
75:7  Warning: Unexpected console statement.  no-console
89:6  Warning: React Hook useEffect has a missing dependency: 'checkForConflicts'. Either include it or remove the dependency array.  react-hooks/exhaustive-deps

./src/components/appointment/AppointmentReminders.tsx
59:23  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
63:20  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
82:7  Warning: Unexpected console statement.  no-console

./src/components/appointment/AppointmentStatusManager.tsx
98:21  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
99:7  Warning: Unexpected console statement.  no-console
119:21  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
120:7  Warning: Unexpected console statement.  no-console

./src/components/client/ClientPreferences.tsx
136:7  Warning: Unexpected console statement.  no-console
168:41  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
170:21  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
171:7  Warning: Unexpected console statement.  no-console
208:49  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any

./src/components/client/ClientTagManager.tsx
21:24  Warning: 'Check' is defined but never used.  @typescript-eslint/no-unused-vars
50:44  Warning: 'clientId' is defined but never used.  @typescript-eslint/no-unused-vars
80:7  Warning: Unexpected console statement.  no-console
96:7  Warning: Unexpected console statement.  no-console
123:7  Warning: Unexpected console statement.  no-console
278:7  Warning: Unexpected console statement.  no-console
295:7  Warning: Unexpected console statement.  no-console

./src/components/dashboard/CRMSummaryCard.tsx
5:10  Warning: 'Badge' is defined but never used.  @typescript-eslint/no-unused-vars
15:3  Warning: 'XCircle' is defined but never used.  @typescript-eslint/no-unused-vars
66:13  Warning: 'tomorrow' is assigned a value but never used.  @typescript-eslint/no-unused-vars
84:59  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
85:62  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
86:53  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
87:67  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
89:23  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
90:36  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
92:27  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
93:40  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
97:48  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
110:46  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
121:46  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
133:23  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
135:24  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
150:7  Warning: Unexpected console statement.  no-console

./src/components/finance/FinancialReports.tsx
32:3  Warning: 'TrendingDown' is defined but never used.  @typescript-eslint/no-unused-vars
37:3  Warning: 'Users' is defined but never used.  @typescript-eslint/no-unused-vars
40:27  Warning: 'startOfMonth' is defined but never used.  @typescript-eslint/no-unused-vars
40:41  Warning: 'endOfMonth' is defined but never used.  @typescript-eslint/no-unused-vars
40:53  Warning: 'subMonths' is defined but never used.  @typescript-eslint/no-unused-vars
41:10  Warning: 'zhCN' is defined but never used.  @typescript-eslint/no-unused-vars
78:7  Warning: 'COLORS' is assigned a value but never used.  @typescript-eslint/no-unused-vars
87:24  Warning: 'setSelectedYear' is assigned a value but never used.  @typescript-eslint/no-unused-vars
147:7  Warning: Unexpected console statement.  no-console
159:5  Warning: Unexpected console statement.  no-console

./src/components/finance/PaymentProcessor.tsx
3:20  Warning: 'useEffect' is defined but never used.  @typescript-eslint/no-unused-vars
4:29  Warning: 'CardHeader' is defined but never used.  @typescript-eslint/no-unused-vars
4:41  Warning: 'CardTitle' is defined but never used.  @typescript-eslint/no-unused-vars
9:10  Warning: 'Badge' is defined but never used.  @typescript-eslint/no-unused-vars
29:3  Warning: 'Calendar' is defined but never used.  @typescript-eslint/no-unused-vars
31:3  Warning: 'AlertCircle' is defined but never used.  @typescript-eslint/no-unused-vars
144:21  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
145:7  Warning: Unexpected console statement.  no-console
153:64  Warning: Prefer using nullish coalescing operator (`??`) instead of a logical or (`||`), as it is a safer operator.  @typescript-eslint/prefer-nullish-coalescing
157:63  Warning: Prefer using nullish coalescing operator (`??`) instead of a logical or (`||`), as it is a safer operator.  @typescript-eslint/prefer-nullish-coalescing

./src/components/layout/MobileLayout.tsx
101:10  Warning: 'isMobile' is assigned a value but never used.  @typescript-eslint/no-unused-vars
165:22  Warning: Prefer using nullish coalescing operator (`??`) instead of a logical or (`||`), as it is a safer operator.  @typescript-eslint/prefer-nullish-coalescing
377:76  Warning: Prefer using nullish coalescing operator (`??`) instead of a logical or (`||`), as it is a safer operator.  @typescript-eslint/prefer-nullish-coalescing

./src/components/layout/app-sidebar.tsx
67:31  Warning: '_tenantId' is defined but never used.  @typescript-eslint/no-unused-vars

./src/components/layout/page-header.tsx
42:37  Warning: Prefer using nullish coalescing operator (`??`) instead of a logical or (`||`), as it is a safer operator.  @typescript-eslint/prefer-nullish-coalescing

./src/components/layout/page-layout.tsx
51:43  Warning: Prefer using nullish coalescing operator (`??`) instead of a logical or (`||`), as it is a safer operator.  @typescript-eslint/prefer-nullish-coalescing

./src/components/layout/stats-cards-grid.tsx
69:67  Warning: Prefer using nullish coalescing operator (`??`) instead of a logical or (`||`), as it is a safer operator.  @typescript-eslint/prefer-nullish-coalescing
76:32  Warning: Prefer using nullish coalescing operator (`??`) instead of a logical or (`||`), as it is a safer operator.  @typescript-eslint/prefer-nullish-coalescing

./src/components/mobile/TouchOptimizedComponents.tsx
47:33  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
68:30  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
161:56  Warning: Prefer using nullish coalescing operator (`??`) instead of a logical or (`||`), as it is a safer operator.  @typescript-eslint/prefer-nullish-coalescing
272:33  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any

./src/components/modals/AppointmentModal.tsx
38:24  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
54:74  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
83:46  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
91:54  Warning: Prefer using nullish coalescing operator (`??`) instead of a logical or (`||`), as it is a safer operator.  @typescript-eslint/prefer-nullish-coalescing
103:52  Warning: Prefer using nullish coalescing operator (`??`) instead of a logical or (`||`), as it is a safer operator.  @typescript-eslint/prefer-nullish-coalescing
123:31  Warning: Prefer using nullish coalescing operator (`??`) instead of a logical or (`||`), as it is a safer operator.  @typescript-eslint/prefer-nullish-coalescing
125:35  Warning: Prefer using nullish coalescing operator (`??`) instead of a logical or (`||`), as it is a safer operator.  @typescript-eslint/prefer-nullish-coalescing
125:58  Warning: Prefer using nullish coalescing operator (`??`) instead of a logical or (`||`), as it is a safer operator.  @typescript-eslint/prefer-nullish-coalescing
126:47  Warning: Prefer using nullish coalescing operator (`??`) instead of a logical or (`||`), as it is a safer operator.  @typescript-eslint/prefer-nullish-coalescing
127:43  Warning: Prefer using nullish coalescing operator (`??`) instead of a logical or (`||`), as it is a safer operator.  @typescript-eslint/prefer-nullish-coalescing
179:68  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
210:11  Warning: 'toastId' is assigned a value but never used.  @typescript-eslint/no-unused-vars
242:7  Warning: Unexpected console statement.  no-console

./src/components/modals/ClientModal.tsx
8:39  Warning: 'MapPin' is defined but never used.  @typescript-eslint/no-unused-vars
11:24  Warning: 'AddressData' is defined but never used.  @typescript-eslint/no-unused-vars
11:37  Warning: 'formatAddress' is defined but never used.  @typescript-eslint/no-unused-vars
11:52  Warning: 'isAddressEmpty' is defined but never used.  @typescript-eslint/no-unused-vars
99:46  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
107:54  Warning: Prefer using nullish coalescing operator (`??`) instead of a logical or (`||`), as it is a safer operator.  @typescript-eslint/prefer-nullish-coalescing
119:52  Warning: Prefer using nullish coalescing operator (`??`) instead of a logical or (`||`), as it is a safer operator.  @typescript-eslint/prefer-nullish-coalescing
129:36  Warning: Prefer using nullish coalescing operator (`??`) instead of a logical or (`||`), as it is a safer operator.  @typescript-eslint/prefer-nullish-coalescing
130:52  Warning: Prefer using nullish coalescing operator (`??`) instead of a logical or (`||`), as it is a safer operator.  @typescript-eslint/prefer-nullish-coalescing
131:54  Warning: Prefer using nullish coalescing operator (`??`) instead of a logical or (`||`), as it is a safer operator.  @typescript-eslint/prefer-nullish-coalescing
132:54  Warning: Prefer using nullish coalescing operator (`??`) instead of a logical or (`||`), as it is a safer operator.  @typescript-eslint/prefer-nullish-coalescing
133:34  Warning: Prefer using nullish coalescing operator (`??`) instead of a logical or (`||`), as it is a safer operator.  @typescript-eslint/prefer-nullish-coalescing
134:54  Warning: Prefer using nullish coalescing operator (`??`) instead of a logical or (`||`), as it is a safer operator.  @typescript-eslint/prefer-nullish-coalescing
135:48  Warning: Prefer using nullish coalescing operator (`??`) instead of a logical or (`||`), as it is a safer operator.  @typescript-eslint/prefer-nullish-coalescing
136:40  Warning: Prefer using nullish coalescing operator (`??`) instead of a logical or (`||`), as it is a safer operator.  @typescript-eslint/prefer-nullish-coalescing
139:70  Warning: Prefer using nullish coalescing operator (`??`) instead of a logical or (`||`), as it is a safer operator.  @typescript-eslint/prefer-nullish-coalescing
140:72  Warning: Prefer using nullish coalescing operator (`??`) instead of a logical or (`||`), as it is a safer operator.  @typescript-eslint/prefer-nullish-coalescing
141:36  Warning: Prefer using nullish coalescing operator (`??`) instead of a logical or (`||`), as it is a safer operator.  @typescript-eslint/prefer-nullish-coalescing
143:56  Warning: Prefer using nullish coalescing operator (`??`) instead of a logical or (`||`), as it is a safer operator.  @typescript-eslint/prefer-nullish-coalescing
189:11  Warning: 'toastId' is assigned a value but never used.  @typescript-eslint/no-unused-vars
217:21  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
218:7  Warning: Unexpected console statement.  no-console
380:43  Warning: Prefer using nullish coalescing operator (`??`) instead of a logical or (`||`), as it is a safer operator.  @typescript-eslint/prefer-nullish-coalescing
381:45  Warning: Prefer using nullish coalescing operator (`??`) instead of a logical or (`||`), as it is a safer operator.  @typescript-eslint/prefer-nullish-coalescing
392:44  Warning: Prefer using nullish coalescing operator (`??`) instead of a logical or (`||`), as it is a safer operator.  @typescript-eslint/prefer-nullish-coalescing
393:46  Warning: Prefer using nullish coalescing operator (`??`) instead of a logical or (`||`), as it is a safer operator.  @typescript-eslint/prefer-nullish-coalescing

./src/components/modals/InvoiceModal.tsx
25:20  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
63:46  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
83:54  Warning: Prefer using nullish coalescing operator (`??`) instead of a logical or (`||`), as it is a safer operator.  @typescript-eslint/prefer-nullish-coalescing
95:52  Warning: Prefer using nullish coalescing operator (`??`) instead of a logical or (`||`), as it is a safer operator.  @typescript-eslint/prefer-nullish-coalescing
155:7  Warning: Unexpected console statement.  no-console
213:7  Warning: Unexpected console statement.  no-console

./src/components/modals/PaymentModal.tsx
33:20  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
70:46  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
78:54  Warning: Prefer using nullish coalescing operator (`??`) instead of a logical or (`||`), as it is a safer operator.  @typescript-eslint/prefer-nullish-coalescing
90:52  Warning: Prefer using nullish coalescing operator (`??`) instead of a logical or (`||`), as it is a safer operator.  @typescript-eslint/prefer-nullish-coalescing
112:46  Warning: Prefer using nullish coalescing operator (`??`) instead of a logical or (`||`), as it is a safer operator.  @typescript-eslint/prefer-nullish-coalescing
113:52  Warning: Prefer using nullish coalescing operator (`??`) instead of a logical or (`||`), as it is a safer operator.  @typescript-eslint/prefer-nullish-coalescing
114:66  Warning: Prefer using nullish coalescing operator (`??`) instead of a logical or (`||`), as it is a safer operator.  @typescript-eslint/prefer-nullish-coalescing
137:7  Warning: Unexpected console statement.  no-console
194:7  Warning: Unexpected console statement.  no-console

./src/components/modals/TreatmentModal.tsx
12:10  Warning: 'EnhancedInput' is defined but never used.  @typescript-eslint/no-unused-vars
12:25  Warning: 'EnhancedTextarea' is defined but never used.  @typescript-eslint/no-unused-vars
12:43  Warning: 'EnhancedSelect' is defined but never used.  @typescript-eslint/no-unused-vars
13:10  Warning: 'validateForm' is defined but never used.  @typescript-eslint/no-unused-vars
13:24  Warning: 'validateField' is defined but never used.  @typescript-eslint/no-unused-vars
13:39  Warning: 'COMMON_RULES' is defined but never used.  @typescript-eslint/no-unused-vars
13:53  Warning: 'ValidationRule' is defined but never used.  @typescript-eslint/no-unused-vars
68:51  Warning: Prefer using nullish coalescing operator (`??`) instead of a logical or (`||`), as it is a safer operator.  @typescript-eslint/prefer-nullish-coalescing
69:67  Warning: Prefer using nullish coalescing operator (`??`) instead of a logical or (`||`), as it is a safer operator.  @typescript-eslint/prefer-nullish-coalescing
134:21  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
135:7  Warning: Unexpected console statement.  no-console

./src/components/treatment/TreatmentCategoryManager.tsx
72:52  Warning: 'onCategorySelect' is defined but never used.  @typescript-eslint/no-unused-vars
138:7  Warning: Unexpected console statement.  no-console
153:13  Warning: 'categoryData' is assigned a value but never used.  @typescript-eslint/no-unused-vars
182:21  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
183:7  Warning: Unexpected console statement.  no-console
193:41  Warning: Prefer using nullish coalescing operator (`??`) instead of a logical or (`||`), as it is a safer operator.  @typescript-eslint/prefer-nullish-coalescing
201:31  Warning: 'categoryId' is defined but never used.  @typescript-eslint/no-unused-vars
213:21  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
214:7  Warning: Unexpected console statement.  no-console
232:21  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
233:7  Warning: Unexpected console statement.  no-console
250:63  Warning: Prefer using nullish coalescing operator (`??`) instead of a logical or (`||`), as it is a safer operator.  @typescript-eslint/prefer-nullish-coalescing
391:45  Warning: Prefer using nullish coalescing operator (`??`) instead of a logical or (`||`), as it is a safer operator.  @typescript-eslint/prefer-nullish-coalescing

./src/components/treatment/TreatmentPackageManager.tsx
82:51  Warning: 'onPackageSelect' is defined but never used.  @typescript-eslint/no-unused-vars
146:7  Warning: Unexpected console statement.  no-console
230:13  Warning: 'packageData' is assigned a value but never used.  @typescript-eslint/no-unused-vars
248:21  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
249:7  Warning: Unexpected console statement.  no-console
259:44  Warning: Prefer using nullish coalescing operator (`??`) instead of a logical or (`||`), as it is a safer operator.  @typescript-eslint/prefer-nullish-coalescing
270:31  Warning: 'packageId' is defined but never used.  @typescript-eslint/no-unused-vars
278:21  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
279:7  Warning: Unexpected console statement.  no-console
533:46  Warning: Prefer using nullish coalescing operator (`??`) instead of a logical or (`||`), as it is a safer operator.  @typescript-eslint/prefer-nullish-coalescing

./src/components/ui/address-input.tsx
47:3  Warning: 'showMap' is assigned a value but never used.  @typescript-eslint/no-unused-vars
99:7  Warning: Unexpected console statement.  no-console
136:11  Warning: Unexpected console statement.  no-console
346:32  Warning: Prefer using nullish coalescing operator (`??`) instead of a logical or (`||`), as it is a safer operator.  @typescript-eslint/prefer-nullish-coalescing

./src/components/ui/appointment-type-select.tsx
5:10  Warning: 'Input' is defined but never used.  @typescript-eslint/no-unused-vars
63:9  Warning: Unexpected console statement.  no-console
69:9  Warning: 'saveCustomTypes' is assigned a value but never used.  @typescript-eslint/no-unused-vars
73:7  Warning: Unexpected console statement.  no-console

./src/components/ui/bulk-actions.tsx
35:3  Warning: 'Users' is defined but never used.  @typescript-eslint/no-unused-vars
99:21  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
100:7  Warning: Unexpected console statement.  no-console
151:41  Warning: Prefer using nullish coalescing operator (`??`) instead of a logical or (`||`), as it is a safer operator.  @typescript-eslint/prefer-nullish-coalescing
209:49  Warning: Prefer using nullish coalescing operator (`??`) instead of a logical or (`||`), as it is a safer operator.  @typescript-eslint/prefer-nullish-coalescing
212:55  Warning: Prefer using nullish coalescing operator (`??`) instead of a logical or (`||`), as it is a safer operator.  @typescript-eslint/prefer-nullish-coalescing

./src/components/ui/calendar.tsx
30:43  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any

./src/components/ui/chart.tsx
12:4  Warning: 'key' is defined but never used.  @typescript-eslint/no-unused-vars
50:31  Warning: Prefer using nullish coalescing operator (`??`) instead of a logical or (`||`), as it is a safer operator.  @typescript-eslint/prefer-nullish-coalescing
75:34  Warning: Prefer using nullish coalescing operator (`??`) instead of a logical or (`||`), as it is a safer operator.  @typescript-eslint/prefer-nullish-coalescing
92:66  Warning: Prefer using nullish coalescing operator (`??`) instead of a logical or (`||`), as it is a safer operator.  @typescript-eslint/prefer-nullish-coalescing
138:29  Warning: Prefer using nullish coalescing operator (`??`) instead of a logical or (`||`), as it is a safer operator.  @typescript-eslint/prefer-nullish-coalescing
138:46  Warning: Prefer using nullish coalescing operator (`??`) instead of a logical or (`||`), as it is a safer operator.  @typescript-eslint/prefer-nullish-coalescing
138:60  Warning: Prefer using nullish coalescing operator (`??`) instead of a logical or (`||`), as it is a safer operator.  @typescript-eslint/prefer-nullish-coalescing
142:55  Warning: Prefer using nullish coalescing operator (`??`) instead of a logical or (`||`), as it is a safer operator.  @typescript-eslint/prefer-nullish-coalescing
184:34  Warning: Prefer using nullish coalescing operator (`??`) instead of a logical or (`||`), as it is a safer operator.  @typescript-eslint/prefer-nullish-coalescing
184:47  Warning: Prefer using nullish coalescing operator (`??`) instead of a logical or (`||`), as it is a safer operator.  @typescript-eslint/prefer-nullish-coalescing
184:63  Warning: Prefer using nullish coalescing operator (`??`) instead of a logical or (`||`), as it is a safer operator.  @typescript-eslint/prefer-nullish-coalescing
186:40  Warning: Prefer using nullish coalescing operator (`??`) instead of a logical or (`||`), as it is a safer operator.  @typescript-eslint/prefer-nullish-coalescing
233:44  Warning: Prefer using nullish coalescing operator (`??`) instead of a logical or (`||`), as it is a safer operator.  @typescript-eslint/prefer-nullish-coalescing
280:32  Warning: Prefer using nullish coalescing operator (`??`) instead of a logical or (`||`), as it is a safer operator.  @typescript-eslint/prefer-nullish-coalescing
280:48  Warning: Prefer using nullish coalescing operator (`??`) instead of a logical or (`||`), as it is a safer operator.  @typescript-eslint/prefer-nullish-coalescing

./src/components/ui/client-search-select.tsx
3:31  Warning: 'useRef' is defined but never used.  @typescript-eslint/no-unused-vars
5:10  Warning: 'Input' is defined but never used.  @typescript-eslint/no-unused-vars
51:10  Warning: 'loading' is assigned a value but never used.  @typescript-eslint/no-unused-vars
69:7  Warning: Unexpected console statement.  no-console

./src/components/ui/data-table.tsx
28:9  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
29:21  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any

./src/components/ui/enhanced-input.tsx
106:40  Warning: Prefer using nullish coalescing operator (`??`) instead of a logical or (`||`), as it is a safer operator.  @typescript-eslint/prefer-nullish-coalescing
113:40  Warning: Prefer using nullish coalescing operator (`??`) instead of a logical or (`||`), as it is a safer operator.  @typescript-eslint/prefer-nullish-coalescing
118:30  Warning: Prefer using nullish coalescing operator (`??`) instead of a logical or (`||`), as it is a safer operator.  @typescript-eslint/prefer-nullish-coalescing
225:40  Warning: Prefer using nullish coalescing operator (`??`) instead of a logical or (`||`), as it is a safer operator.  @typescript-eslint/prefer-nullish-coalescing
232:40  Warning: Prefer using nullish coalescing operator (`??`) instead of a logical or (`||`), as it is a safer operator.  @typescript-eslint/prefer-nullish-coalescing
237:30  Warning: Prefer using nullish coalescing operator (`??`) instead of a logical or (`||`), as it is a safer operator.  @typescript-eslint/prefer-nullish-coalescing
328:40  Warning: Prefer using nullish coalescing operator (`??`) instead of a logical or (`||`), as it is a safer operator.  @typescript-eslint/prefer-nullish-coalescing
332:30  Warning: Prefer using nullish coalescing operator (`??`) instead of a logical or (`||`), as it is a safer operator.  @typescript-eslint/prefer-nullish-coalescing

./src/components/ui/enhanced-search.tsx
31:27  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
60:57  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
71:24  Warning: Prefer using nullish coalescing operator (`??`) instead of a logical or (`||`), as it is a safer operator.  @typescript-eslint/prefer-nullish-coalescing
79:6  Warning: React Hook useCallback has a missing dependency: 'updateFilters'. Either include it or remove the dependency array.  react-hooks/exhaustive-deps
82:63  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
86:6  Warning: React Hook useCallback has a missing dependency: 'updateFilters'. Either include it or remove the dependency array.  react-hooks/exhaustive-deps
92:6  Warning: React Hook useCallback has a missing dependency: 'updateFilters'. Either include it or remove the dependency array.  react-hooks/exhaustive-deps
98:6  Warning: React Hook useCallback has a missing dependency: 'updateFilters'. Either include it or remove the dependency array.  react-hooks/exhaustive-deps
138:6  Warning: React Hook useCallback has a missing dependency: 'updateFilters'. Either include it or remove the dependency array.  react-hooks/exhaustive-deps
177:47  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
238:66  Warning: Prefer using nullish coalescing operator (`??`) instead of a logical or (`||`), as it is a safer operator.  @typescript-eslint/prefer-nullish-coalescing
267:66  Warning: Prefer using nullish coalescing operator (`??`) instead of a logical or (`||`), as it is a safer operator.  @typescript-eslint/prefer-nullish-coalescing
314:91  Warning: Prefer using nullish coalescing operator (`??`) instead of a logical or (`||`), as it is a safer operator.  @typescript-eslint/prefer-nullish-coalescing
325:28  Warning: Prefer using nullish coalescing operator (`??`) instead of a logical or (`||`), as it is a safer operator.  @typescript-eslint/prefer-nullish-coalescing

./src/components/ui/global-search.tsx
6:10  Warning: 'Input' is defined but never used.  @typescript-eslint/no-unused-vars
23:3  Warning: 'Settings' is defined but never used.  @typescript-eslint/no-unused-vars
72:9  Warning: Unexpected console statement.  no-console
201:7  Warning: Unexpected console statement.  no-console

./src/components/ui/mobile-table.tsx
5:10  Warning: 'Badge' is defined but never used.  @typescript-eslint/no-unused-vars
6:10  Warning: 'Button' is defined but never used.  @typescript-eslint/no-unused-vars
10:9  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
11:22  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
45:9  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
47:28  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any

./src/components/ui/progress.tsx
25:57  Warning: Prefer using nullish coalescing operator (`??`) instead of a logical or (`||`), as it is a safer operator.  @typescript-eslint/prefer-nullish-coalescing

./src/components/ui/responsive-layout.tsx
7:16  Warning: 'X' is defined but never used.  @typescript-eslint/no-unused-vars
177:20  Warning: Prefer using nullish coalescing operator (`??`) instead of a logical or (`||`), as it is a safer operator.  @typescript-eslint/prefer-nullish-coalescing

./src/components/ui/table/data-table-date-filter.tsx
94:36  Warning: Prefer using nullish coalescing operator (`??`) instead of a logical or (`||`), as it is a safer operator.  @typescript-eslint/prefer-nullish-coalescing
113:33  Warning: Prefer using nullish coalescing operator (`??`) instead of a logical or (`||`), as it is a safer operator.  @typescript-eslint/prefer-nullish-coalescing
131:51  Warning: Prefer using nullish coalescing operator (`??`) instead of a logical or (`||`), as it is a safer operator.  @typescript-eslint/prefer-nullish-coalescing

./src/components/ui/toggle-group.tsx
56:37  Warning: Prefer using nullish coalescing operator (`??`) instead of a logical or (`||`), as it is a safer operator.  @typescript-eslint/prefer-nullish-coalescing
57:31  Warning: Prefer using nullish coalescing operator (`??`) instead of a logical or (`||`), as it is a safer operator.  @typescript-eslint/prefer-nullish-coalescing
60:36  Warning: Prefer using nullish coalescing operator (`??`) instead of a logical or (`||`), as it is a safer operator.  @typescript-eslint/prefer-nullish-coalescing
61:30  Warning: Prefer using nullish coalescing operator (`??`) instead of a logical or (`||`), as it is a safer operator.  @typescript-eslint/prefer-nullish-coalescing

./src/components/ui/treatment-search-select.tsx
52:10  Warning: 'loading' is assigned a value but never used.  @typescript-eslint/no-unused-vars
64:7  Warning: Unexpected console statement.  no-console
89:41  Warning: Prefer using nullish coalescing operator (`??`) instead of a logical or (`||`), as it is a safer operator.  @typescript-eslint/prefer-nullish-coalescing

./src/components/user-avatar-profile.tsx
21:42  Warning: Prefer using nullish coalescing operator (`??`) instead of a logical or (`||`), as it is a safer operator.  @typescript-eslint/prefer-nullish-coalescing
21:69  Warning: Prefer using nullish coalescing operator (`??`) instead of a logical or (`||`), as it is a safer operator.  @typescript-eslint/prefer-nullish-coalescing
23:55  Warning: Prefer using nullish coalescing operator (`??`) instead of a logical or (`||`), as it is a safer operator.  @typescript-eslint/prefer-nullish-coalescing
29:68  Warning: Prefer using nullish coalescing operator (`??`) instead of a logical or (`||`), as it is a safer operator.  @typescript-eslint/prefer-nullish-coalescing
31:51  Warning: Prefer using nullish coalescing operator (`??`) instead of a logical or (`||`), as it is a safer operator.  @typescript-eslint/prefer-nullish-coalescing

./src/features/auth/components/github-auth-button.tsx
9:9  Warning: 'callbackUrl' is assigned a value but never used.  @typescript-eslint/no-unused-vars
16:22  Warning: Unexpected console statement.  no-console

./src/features/auth/components/user-auth-form.tsx
28:9  Warning: 'callbackUrl' is assigned a value but never used.  @typescript-eslint/no-unused-vars
38:27  Warning: 'data' is defined but never used.  @typescript-eslint/no-unused-vars
40:7  Warning: Unexpected console statement.  no-console

./src/features/kanban/components/board-column.tsx
100:51  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any

./src/features/kanban/components/kanban-board.tsx
181:44  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
191:47  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
213:44  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any

./src/features/products/components/product-form.tsx
66:29  Warning: Prefer using nullish coalescing operator (`??`) instead of a logical or (`||`), as it is a safer operator.  @typescript-eslint/prefer-nullish-coalescing
67:37  Warning: Prefer using nullish coalescing operator (`??`) instead of a logical or (`||`), as it is a safer operator.  @typescript-eslint/prefer-nullish-coalescing
68:31  Warning: Prefer using nullish coalescing operator (`??`) instead of a logical or (`||`), as it is a safer operator.  @typescript-eslint/prefer-nullish-coalescing
69:43  Warning: Prefer using nullish coalescing operator (`??`) instead of a logical or (`||`), as it is a safer operator.  @typescript-eslint/prefer-nullish-coalescing
77:21  Warning: 'values' is defined but never used.  @typescript-eslint/no-unused-vars

./src/features/profile/components/profile-create-form.tsx
36:16  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
40:9  Warning: 'params' is assigned a value but never used.  @typescript-eslint/no-unused-vars
41:9  Warning: 'router' is assigned a value but never used.  @typescript-eslint/no-unused-vars
42:19  Warning: 'setLoading' is assigned a value but never used.  @typescript-eslint/no-unused-vars
43:10  Warning: 'open' is assigned a value but never used.  @typescript-eslint/no-unused-vars
48:10  Warning: 'previousStep' is assigned a value but never used.  @typescript-eslint/no-unused-vars

./src/hooks/use-callback-ref.tsx
Error: Parsing error: ESLint was configured to run on `<tsconfigRootDir>/src/hooks/use-callback-ref.tsx` using `parserOptions.project`: <tsconfigRootDir>/tsconfig.json
However, that TSConfig does not include this file. Either:
- Change ESLint's list of included files to not include this file
- Change that TSConfig to include this file
- Create a new TSConfig that includes this file and include it in your parserOptions.project
See the typescript-eslint docs for more info: https://typescript-eslint.io/troubleshooting/typed-linting#i-get-errors-telling-me-eslint-was-configured-to-run--however-that-tsconfig-does-not--none-of-those-tsconfigs-include-this-file

./src/hooks/use-multistep-form.tsx
3:62  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any

./src/lib/ai/intelligent-automation.ts
66:49  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
343:42  Warning: 'treatmentId' is defined but never used.  @typescript-eslint/no-unused-vars
343:63  Warning: 'preferredDate' is defined but never used.  @typescript-eslint/no-unused-vars
352:41  Warning: 'treatmentId' is defined but never used.  @typescript-eslint/no-unused-vars
352:62  Warning: 'preferredDate' is defined but never used.  @typescript-eslint/no-unused-vars
361:44  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
410:36  Warning: 'clientProfile' is defined but never used.  @typescript-eslint/no-unused-vars
410:51  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
420:20  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
421:23  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
422:21  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
461:34  Warning: 'treatmentId' is defined but never used.  @typescript-eslint/no-unused-vars
470:41  Warning: 'treatmentId' is defined but never used.  @typescript-eslint/no-unused-vars
480:38  Warning: 'treatmentId' is defined but never used.  @typescript-eslint/no-unused-vars
490:49  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
507:47  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
511:30  Warning: Prefer using nullish coalescing operator (`??`) instead of a logical or (`||`), as it is a safer operator.  @typescript-eslint/prefer-nullish-coalescing
521:53  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
538:41  Warning: 'clientId' is defined but never used.  @typescript-eslint/no-unused-vars
548:53  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
600:27  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
617:41  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
617:56  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
621:66  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
621:71  Warning: 'prediction' is defined but never used.  @typescript-eslint/no-unused-vars
621:83  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
639:38  Warning: 'treatmentId' is defined but never used.  @typescript-eslint/no-unused-vars
649:41  Warning: 'treatmentId' is defined but never used.  @typescript-eslint/no-unused-vars
659:45  Warning: 'treatmentId' is defined but never used.  @typescript-eslint/no-unused-vars
668:46  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any

./src/lib/analytics/business-intelligence.ts
73:50  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
211:60  Warning: 'lastMonth' is defined but never used.  @typescript-eslint/no-unused-vars
537:31  Warning: Prefer using nullish coalescing operator (`??`) instead of a logical or (`||`), as it is a safer operator.  @typescript-eslint/prefer-nullish-coalescing

./src/lib/api-handler.ts
75:56  Warning: Prefer using nullish coalescing operator (`??`) instead of a logical or (`||`), as it is a safer operator.  @typescript-eslint/prefer-nullish-coalescing
76:54  Warning: Prefer using nullish coalescing operator (`??`) instead of a logical or (`||`), as it is a safer operator.  @typescript-eslint/prefer-nullish-coalescing
76:90  Warning: Prefer using nullish coalescing operator (`??`) instead of a logical or (`||`), as it is a safer operator.  @typescript-eslint/prefer-nullish-coalescing

./src/lib/auth/rbac.ts
9:21  Warning: 'AccessPurpose' is defined but never used.  @typescript-eslint/no-unused-vars
629:44  Warning: Prefer using nullish coalescing operator (`??`) instead of a logical or (`||`), as it is a safer operator.  @typescript-eslint/prefer-nullish-coalescing

./src/lib/cache.ts
249:16  Warning: 'key' is defined but never used.  @typescript-eslint/no-unused-vars
254:16  Warning: 'key' is defined but never used.  @typescript-eslint/no-unused-vars
254:29  Warning: 'value' is defined but never used.  @typescript-eslint/no-unused-vars
254:39  Warning: 'options' is defined but never used.  @typescript-eslint/no-unused-vars
259:16  Warning: 'key' is defined but never used.  @typescript-eslint/no-unused-vars
264:15  Warning: 'namespace' is defined but never used.  @typescript-eslint/no-unused-vars
269:26  Warning: 'tags' is defined but never used.  @typescript-eslint/no-unused-vars
274:16  Warning: 'key' is defined but never used.  @typescript-eslint/no-unused-vars
279:13  Warning: 'key' is defined but never used.  @typescript-eslint/no-unused-vars

./src/lib/compliance/automation.ts
589:35  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any

./src/lib/compliance/hipaa.ts
238:41  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
238:90  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
265:41  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
265:90  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
310:5  Warning: Forbidden non-null assertion.  @typescript-eslint/no-non-null-assertion
505:49  Warning: Prefer using nullish coalescing operator (`??`) instead of a logical or (`||`), as it is a safer operator.  @typescript-eslint/prefer-nullish-coalescing
524:45  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
524:86  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
527:22  Warning: 'value' is assigned a value but never used.  @typescript-eslint/no-unused-vars
540:38  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
551:5  Warning: 'tableName' is defined but never used.  @typescript-eslint/no-unused-vars
552:5  Warning: 'purpose' is defined but never used.  @typescript-eslint/no-unused-vars

./src/lib/currency.ts
14:56  Warning: Prefer using nullish coalescing operator (`??`) instead of a logical or (`||`), as it is a safer operator.  @typescript-eslint/prefer-nullish-coalescing

./src/lib/data-export.ts
9:23  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
9:33  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
16:9  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
24:29  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
24:54  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
24:64  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
24:86  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
85:3  Warning: Unexpected console statement.  no-console
86:66  Warning: Prefer using nullish coalescing operator (`??`) instead of a logical or (`||`), as it is a safer operator.  @typescript-eslint/prefer-nullish-coalescing
129:29  Warning: Prefer using nullish coalescing operator (`??`) instead of a logical or (`||`), as it is a safer operator.  @typescript-eslint/prefer-nullish-coalescing
249:69  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
258:24  Warning: Prefer using nullish coalescing operator (`??`) instead of a logical or (`||`), as it is a safer operator.  @typescript-eslint/prefer-nullish-coalescing
273:23  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
274:25  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
274:33  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
279:9  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
341:20  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
347:20  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
351:43  Warning: Prefer using nullish coalescing operator (`??`) instead of a logical or (`||`), as it is a safer operator.  @typescript-eslint/prefer-nullish-coalescing

./src/lib/database/performance.ts
53:36  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
104:28  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
104:36  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
196:17  Warning: Forbidden non-null assertion.  @typescript-eslint/no-non-null-assertion
197:58  Warning: Forbidden non-null assertion.  @typescript-eslint/no-non-null-assertion
206:28  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
206:36  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
214:44  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
251:30  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
251:38  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
382:28  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
382:36  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
391:28  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
391:36  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
402:30  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
402:38  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
432:15  Warning: 'data' is assigned a value but never used.  @typescript-eslint/no-unused-vars

./src/lib/errors.ts
206:5  Warning: Unexpected console statement.  no-console

./src/lib/form-validation.ts
15:20  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
57:38  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
129:51  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
236:63  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
241:65  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
246:24  Warning: Prefer using nullish coalescing operator (`??`) instead of a logical or (`||`), as it is a safer operator.  @typescript-eslint/prefer-nullish-coalescing
253:58  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any

./src/lib/google-maps.ts
32:46  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
80:33  Warning: Prefer using nullish coalescing operator (`??`) instead of a logical or (`||`), as it is a safer operator.  @typescript-eslint/prefer-nullish-coalescing
87:34  Warning: Prefer using nullish coalescing operator (`??`) instead of a logical or (`||`), as it is a safer operator.  @typescript-eslint/prefer-nullish-coalescing
133:23  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
133:36  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
140:77  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
147:27  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
147:46  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
168:5  Warning: Unexpected console statement.  no-console
178:34  Warning: 'reject' is defined but never used.  @typescript-eslint/no-unused-vars
186:19  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
186:32  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
200:5  Warning: Unexpected console statement.  no-console
208:13  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
211:17  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any

./src/lib/integrations/payment-gateways.ts
335:30  Warning: Prefer using nullish coalescing operator (`??`) instead of a logical or (`||`), as it is a safer operator.  @typescript-eslint/prefer-nullish-coalescing
340:38  Warning: 'request' is defined but never used.  @typescript-eslint/no-unused-vars
391:30  Warning: Prefer using nullish coalescing operator (`??`) instead of a logical or (`||`), as it is a safer operator.  @typescript-eslint/prefer-nullish-coalescing
396:38  Warning: 'request' is defined but never used.  @typescript-eslint/no-unused-vars
440:30  Warning: Prefer using nullish coalescing operator (`??`) instead of a logical or (`||`), as it is a safer operator.  @typescript-eslint/prefer-nullish-coalescing
485:30  Warning: Prefer using nullish coalescing operator (`??`) instead of a logical or (`||`), as it is a safer operator.  @typescript-eslint/prefer-nullish-coalescing
529:30  Warning: Prefer using nullish coalescing operator (`??`) instead of a logical or (`||`), as it is a safer operator.  @typescript-eslint/prefer-nullish-coalescing
573:30  Warning: Prefer using nullish coalescing operator (`??`) instead of a logical or (`||`), as it is a safer operator.  @typescript-eslint/prefer-nullish-coalescing

./src/lib/logger.ts
6:10  Warning: 'env' is defined but never used.  @typescript-eslint/no-unused-vars
54:44  Warning: Prefer using nullish coalescing operator (`??`) instead of a logical or (`||`), as it is a safer operator.  @typescript-eslint/prefer-nullish-coalescing
183:9  Warning: Unexpected console statement.  no-console
186:9  Warning: Unexpected console statement.  no-console
189:9  Warning: Unexpected console statement.  no-console
193:9  Warning: Unexpected console statement.  no-console
201:21  Warning: 'entry' is defined but never used.  @typescript-eslint/no-unused-vars
218:23  Warning: 'entry' is defined but never used.  @typescript-eslint/no-unused-vars

./src/lib/monitoring.ts
7:10  Warning: 'env' is defined but never used.  @typescript-eslint/no-unused-vars
400:28  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
403:49  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any

./src/lib/react-query.ts
464:25  Error: Parsing error: '>' expected.

./src/lib/security/monitoring.ts
288:22  Warning: Prefer using nullish coalescing operator (`??`) instead of a logical or (`||`), as it is a safer operator.  @typescript-eslint/prefer-nullish-coalescing
315:71  Warning: Prefer using nullish coalescing operator (`??`) instead of a logical or (`||`), as it is a safer operator.  @typescript-eslint/prefer-nullish-coalescing
355:13  Warning: Forbidden non-null assertion.  @typescript-eslint/no-non-null-assertion

./src/lib/security.ts
10:26  Warning: 'AuthenticationError' is defined but never used.  @typescript-eslint/no-unused-vars
72:13  Warning: 'iv' is assigned a value but never used.  @typescript-eslint/no-unused-vars
129:11  Warning: 'windowStart' is assigned a value but never used.  @typescript-eslint/no-unused-vars
181:55  Warning: Prefer using nullish coalescing operator (`??`) instead of a logical or (`||`), as it is a safer operator.  @typescript-eslint/prefer-nullish-coalescing
182:49  Warning: Prefer using nullish coalescing operator (`??`) instead of a logical or (`||`), as it is a safer operator.  @typescript-eslint/prefer-nullish-coalescing
237:51  Warning: Prefer using nullish coalescing operator (`??`) instead of a logical or (`||`), as it is a safer operator.  @typescript-eslint/prefer-nullish-coalescing
392:34  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
392:40  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any

./src/lib/supabase/queries.ts
9:6  Warning: 'Treatment' is defined but never used.  @typescript-eslint/no-unused-vars
16:6  Warning: 'Invoice' is defined but never used.  @typescript-eslint/no-unused-vars
20:6  Warning: 'Payment' is defined but never used.  @typescript-eslint/no-unused-vars
26:6  Warning: 'ContactLog' is defined but never used.  @typescript-eslint/no-unused-vars
711:43  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
738:40  Warning: Prefer using nullish coalescing operator (`??`) instead of a logical or (`||`), as it is a safer operator.  @typescript-eslint/prefer-nullish-coalescing
751:9  Warning: Prefer using nullish coalescing operator (`??`) instead of a logical or (`||`), as it is a safer operator.  @typescript-eslint/prefer-nullish-coalescing
788:48  Warning: Prefer using nullish coalescing operator (`??`) instead of a logical or (`||`), as it is a safer operator.  @typescript-eslint/prefer-nullish-coalescing
789:49  Warning: Prefer using nullish coalescing operator (`??`) instead of a logical or (`||`), as it is a safer operator.  @typescript-eslint/prefer-nullish-coalescing

./src/lib/supabase/types.ts
362:8  Warning: '_' is defined but never used.  @typescript-eslint/no-unused-vars
365:8  Warning: '_' is defined but never used.  @typescript-eslint/no-unused-vars
368:8  Warning: '_' is defined but never used.  @typescript-eslint/no-unused-vars
371:8  Warning: '_' is defined but never used.  @typescript-eslint/no-unused-vars

./src/lib/test-utils.ts
64:32  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
87:35  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
103:37  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
118:33  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
132:33  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
154:5  Warning: Unexpected console statement.  no-console
160:35  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
163:5  Warning: Unexpected console statement.  no-console
180:22  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
318:36  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
330:37  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any

./src/lib/toast-utils.ts
81:29  Warning: Prefer using nullish coalescing operator (`??`) instead of a logical or (`||`), as it is a safer operator.  @typescript-eslint/prefer-nullish-coalescing

./src/lib/working-hours.ts
35:5  Warning: Unexpected console statement.  no-console
45:5  Warning: Unexpected console statement.  no-console
54:31  Warning: Prefer using nullish coalescing operator (`??`) instead of a logical or (`||`), as it is a safer operator.  @typescript-eslint/prefer-nullish-coalescing

./src/types/data-table.ts
7:24  Warning: 'TData' is defined but never used.  @typescript-eslint/no-unused-vars
7:47  Warning: 'TValue' is defined but never used.  @typescript-eslint/no-unused-vars

info  - Need to disable some ESLint rules? Learn more here: https://nextjs.org/docs/app/api-reference/config/eslint#disabling-rules



### Environment file exists
- **Status**: ✅ PASSED
- **Required**: Yes
- **Command**: `file check`



### Environment example file exists
- **Status**: ✅ PASSED
- **Required**: No
- **Command**: `file check`



### Environment validation script functional
- **Status**: ✅ PASSED
- **Required**: No
- **Command**: `node scripts/validate-environment.js`



### Code quality analysis
- **Status**: ✅ PASSED
- **Required**: No
- **Command**: `pnpm fix:quality`



### Mobile responsiveness analysis
- **Status**: ✅ PASSED
- **Required**: No
- **Command**: `pnpm test:mobile`



### Unit test suite
- **Status**: ⚠️ WARNING
- **Required**: No
- **Command**: `pnpm test:unit --passWithNoTests`
- **Error**: Command failed: pnpm test:unit --passWithNoTests
 ⚠ Warning: Found multiple lockfiles. Selecting /Users/<USER>/package-lock.json.
   Consider removing the lockfiles at:
   * /Users/<USER>/Desktop/nord_new/pnpm-lock.yaml

FAIL __tests__/unit/validation.test.ts
  Validation System
    Common Schemas
      UUID validation
        ✓ should validate correct UUID format (2 ms)
        ✓ should reject invalid UUID format (7 ms)
      Email validation
        ✓ should validate correct email formats (3 ms)
        ✓ should reject invalid email formats (1 ms)
      Phone validation
        ✓ should validate correct phone formats (1 ms)
        ✓ should reject invalid phone formats (1 ms)
      Date validation
        ✓ should validate correct date format
        ✕ should reject invalid date formats (1 ms)
      Time validation
        ✓ should validate correct time format
        ✕ should reject invalid time formats
      Currency validation
        ✓ should validate positive currency amounts
        ✓ should reject invalid currency amounts (1 ms)
      Pagination validation
        ✓ should validate correct pagination parameters (1 ms)
        ✓ should apply default values
        ✓ should reject invalid pagination parameters (1 ms)
    Client Schemas
      Client creation validation
        ✕ should validate correct client data (8 ms)
        ✓ should require mandatory fields
        ✓ should validate field lengths
        ✓ should validate optional fields
      Client update validation
        ✓ should allow partial updates (1 ms)
        ✓ should validate status field (2 ms)
    Treatment Schemas
      Treatment creation validation
        ✓ should validate correct treatment data (1 ms)
        ✓ should validate duration constraints
    Appointment Schemas
      Appointment creation validation
        ✓ should validate correct appointment data (1 ms)
        ✓ should validate time constraints
      Date range validation
        ✓ should validate correct date range (1 ms)
        ✓ should reject invalid date range
    Validation Utilities
      validateRequest
        ✓ should validate correct data (1 ms)
        ✓ should throw ValidationError for invalid data (2 ms)
      validateQuery
        ✓ should validate and convert query parameters (1 ms)
        ✓ should handle array parameters
      sanitizeHtml
        ✓ should escape HTML characters (1 ms)
        ✕ should handle all dangerous characters (1 ms)
      sanitizeText
        ✕ should trim and sanitize text
      safeValidate
        ✓ should return success result for valid data (1 ms)
        ✓ should return failure result for invalid data
    Edge Cases and Security
      ✓ should handle null and undefined values (1 ms)
      ✓ should prevent prototype pollution
      ✓ should handle very large strings (1 ms)
      ✓ should handle special Unicode characters

  ● Validation System › Common Schemas › Date validation › should reject invalid date formats

    expect(received).toThrow()

    Received function did not throw

      109 |
      110 |         invalidDates.forEach(date => {
    > 111 |           expect(() => commonSchemas.date.parse(date)).toThrow()
          |                                                        ^
      112 |         })
      113 |       })
      114 |     })

      at toThrow (__tests__/unit/validation.test.ts:111:56)
          at Array.forEach (<anonymous>)
      at Object.forEach (__tests__/unit/validation.test.ts:110:22)

  ● Validation System › Common Schemas › Time validation › should reject invalid time formats

    expect(received).toThrow()

    Received function did not throw

      133 |
      134 |         invalidTimes.forEach(time => {
    > 135 |           expect(() => commonSchemas.time.parse(time)).toThrow()
          |                                                        ^
      136 |         })
      137 |       })
      138 |     })

      at toThrow (__tests__/unit/validation.test.ts:135:56)
          at Array.forEach (<anonymous>)
      at Object.forEach (__tests__/unit/validation.test.ts:134:22)

  ● Validation System › Client Schemas › Client creation validation › should validate correct client data

    expect(received).not.toThrow()

    Error name:    "ZodError"
    Error message: "[
      {
        \"validation\": \"regex\",
        \"code\": \"invalid_string\",
        \"message\": \"无效的电话号码格式\",
        \"path\": [
          \"emergency_contact_phone\"
        ]
      }
    ]"

          203 |         }
          204 |
        > 205 |         expect(() => clientSchemas.create.parse(validClient)).not.toThrow()
              |                                           ^
          206 |       })
          207 |
          208 |       it('should require mandatory fields', () => {

      at Object.get error [as error] (node_modules/.pnpm/zod@3.24.2/node_modules/zod/lib/types.js:55:31)
      at ZodObject.parse (node_modules/.pnpm/zod@3.24.2/node_modules/zod/lib/types.js:131:22)
      at parse (__tests__/unit/validation.test.ts:205:43)
      at Object.<anonymous> (node_modules/.pnpm/expect@30.0.4/node_modules/expect/build/index.js:1824:9)
      at Object.throwingMatcher [as toThrow] (node_modules/.pnpm/expect@30.0.4/node_modules/expect/build/index.js:2232:93)
      at Object.toThrow (__tests__/unit/validation.test.ts:205:67)
      at Object.toThrow (__tests__/unit/validation.test.ts:205:67)

  ● Validation System › Validation Utilities › sanitizeHtml › should handle all dangerous characters

    expect(received).toBe(expected) // Object.is equality

    Expected: "&lt;&gt;&quot;&#x27;&amp;&#x2F;"
    Received: "&lt;&gt;&quot;&#x27;&&#x2F;"

      412 |         const expected = '&lt;&gt;&quot;&#x27;&amp;&#x2F;'
      413 |
    > 414 |         expect(sanitizeHtml(input)).toBe(expected)
          |                                     ^
      415 |       })
      416 |     })
      417 |

      at Object.toBe (__tests__/unit/validation.test.ts:414:37)

  ● Validation System › Validation Utilities › sanitizeText › should trim and sanitize text

    TypeError: expect(...).not.toStartWith is not a function

      422 |
      423 |         expect(result).not.toContain('<script>')
    > 424 |         expect(result).not.toStartWith(' ')
          |                            ^
      425 |         expect(result).not.toEndWith(' ')
      426 |       })
      427 |     })

      at Object.toStartWith (__tests__/unit/validation.test.ts:424:28)

FAIL __tests__/unit/business-logic.test.ts
  Business Logic Tests
    Fixed Deposit Calculation
      ✓ should calculate the highest fixed deposit amount for same-day appointments (1 ms)
      ✓ should return 0 if no treatments have deposit amounts
      ✓ should handle database errors gracefully (1 ms)
    Appointment Conflict Detection
      ✓ should detect time conflicts correctly
      ✕ should not detect conflicts when excluding specific appointment
      ✓ should handle no conflicts
    Invoice Number Generation
      ✕ should generate unique invoice numbers with correct format
      ✓ should include current date in invoice number (1 ms)
    Consultation Fee Calculation
      ✓ should calculate consultation fees for consultation appointments
      ✓ should not include fees for treatments that do not require consultation
    Legacy Deposit Calculation (Backward Compatibility)
      ✓ should calculate percentage-based deposits correctly (2 ms)
      ✓ should use default 50% if no percentage provided
      ✓ should round to 2 decimal places

  ● Business Logic Tests › Appointment Conflict Detection › should not detect conflicts when excluding specific appointment

    TypeError: query.neq is not a function

      666 |
      667 |     if (excludeId) {
    > 668 |       query = query.neq('id', excludeId)
          |                     ^
      669 |     }
      670 |
      671 |     const { data, error } = await query

      at Object.neq [as checkAppointmentConflict] (src/lib/supabase/queries.ts:668:21)
      at Object.checkAppointmentConflict (__tests__/unit/business-logic.test.ts:101:47)

  ● Business Logic Tests › Invoice Number Generation › should generate unique invoice numbers with correct format

    expect(received).not.toBe(expected) // Object.is equality

    Expected: not "INV-20250723-8351"

      134 |       expect(invoiceNumber1).toMatch(/^INV-\d{8}-\d{4}$/)
      135 |       expect(invoiceNumber2).toMatch(/^INV-\d{8}-\d{4}$/)
    > 136 |       expect(invoiceNumber1).not.toBe(invoiceNumber2)
          |                                  ^
      137 |     })
      138 |
      139 |     it('should include current date in invoice number', () => {

      at Object.toBe (__tests__/unit/business-logic.test.ts:136:34)

PASS __tests__/unit/errors.test.ts
  Error Handling System
    AppError Base Class
      ✓ should create error with message and context (1 ms)
      ✓ should maintain proper stack trace (4 ms)
    Specific Error Classes
      ✓ should create ValidationError with proper properties (1 ms)
      ✓ should create AuthenticationError with proper properties
      ✓ should create AuthorizationError with proper properties
      ✓ should create NotFoundError with proper properties (1 ms)
      ✓ should create ConflictError with proper properties
      ✓ should create RateLimitError with proper properties
      ✓ should create InternalServerError with proper properties (1 ms)
      ✓ should create DatabaseError with proper properties
      ✓ should create ExternalServiceError with proper properties
    Error Response Creation
      ✓ should create standardized error response (1 ms)
      ✓ should create error response without request ID
    Error Classification
      ✓ should identify operational errors
    Error Normalization
      ✓ should return AppError as-is (1 ms)
      ✓ should convert ZodError to ValidationError
      ✓ should convert database errors
      ✓ should handle foreign key constraint violation
      ✓ should handle not null constraint violation (1 ms)
      ✓ should convert generic Error to InternalServerError
      ✓ should handle unknown error types
    API Error Handler
      ✓ should handle operational errors
      ✓ should handle non-operational errors (12 ms)
      ✓ should normalize unknown errors (2 ms)
    Async Handler
      ✓ should handle successful async operations
      ✓ should normalize errors from async operations
    Result Pattern
      ✓ should create success result (1 ms)
      ✓ should create failure result
    Safe Async
      ✓ should return success result for successful operations
      ✓ should return failure result for failed operations (1 ms)
      ✓ should normalize unknown errors

FAIL __tests__/unit/security.test.ts
  ● Test suite failed to run

    ZodError: [
      {
        "code": "invalid_type",
        "expected": "string",
        "received": "undefined",
        "path": [
          "NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY"
        ],
        "message": "Required"
      },
      {
        "code": "invalid_type",
        "expected": "string",
        "received": "undefined",
        "path": [
          "CLERK_SECRET_KEY"
        ],
        "message": "Required"
      }
    ]

      54 |  * This ensures type safety throughout the application
      55 |  */
    > 56 | export const env = envSchema.parse(process.env);
         |                              ^
      57 |
      58 | /**
      59 |  * Type-safe environment variables for client-side use

      at Object.get error [as error] (node_modules/.pnpm/zod@3.24.2/node_modules/zod/lib/types.js:55:31)
      at ZodObject.parse (node_modules/.pnpm/zod@3.24.2/node_modules/zod/lib/types.js:131:22)
      at Object.parse (src/lib/env.ts:56:30)
      at Object.<anonymous> (src/lib/security.ts:42:14)
      at Object.<anonymous> (__tests__/unit/security.test.ts:9:19)

    Errors contained in AggregateError:
     Required

            "code": "invalid_type",
            "expected": "string",
            "message": "Required",
            "path": Array [
              "NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY",
            ],
            "received": "undefined",
          }

     Required

            "code": "invalid_type",
            "expected": "string",
            "message": "Required",
            "path": Array [
              "CLERK_SECRET_KEY",
            ],
            "received": "undefined",
          }


Test Suites: 3 failed, 1 passed, 4 total
Tests:       7 failed, 77 passed, 84 total
Snapshots:   0 total
Time:        0.698 s, estimated 1 s
Ran all test suites matching __tests__/unit.
Force exiting Jest: Have you considered using `--detectOpenHandles` to detect async operations that kept running after all tests finished?



### file: package.json
- **Status**: ✅ PASSED
- **Required**: Yes
- **Command**: `file check`



### file: next.config.ts
- **Status**: ✅ PASSED
- **Required**: Yes
- **Command**: `file check`



### file: tsconfig.json
- **Status**: ✅ PASSED
- **Required**: Yes
- **Command**: `file check`



### file: tailwind.config.ts
- **Status**: ✅ PASSED
- **Required**: Yes
- **Command**: `file check`



### file: Dockerfile
- **Status**: ✅ PASSED
- **Required**: Yes
- **Command**: `file check`



### file: docker-compose.yml
- **Status**: ✅ PASSED
- **Required**: Yes
- **Command**: `file check`



### file: .env.example
- **Status**: ✅ PASSED
- **Required**: Yes
- **Command**: `file check`



### file: PRODUCTION_DEPLOYMENT_GUIDE.md
- **Status**: ✅ PASSED
- **Required**: Yes
- **Command**: `file check`



### file: PRODUCTION_DEPLOYMENT_CHECKLIST.md
- **Status**: ✅ PASSED
- **Required**: Yes
- **Command**: `file check`



### directory: src
- **Status**: ✅ PASSED
- **Required**: Yes
- **Command**: `file check`



### directory: src/app
- **Status**: ✅ PASSED
- **Required**: Yes
- **Command**: `file check`



### directory: src/components
- **Status**: ✅ PASSED
- **Required**: Yes
- **Command**: `file check`



### directory: src/lib
- **Status**: ✅ PASSED
- **Required**: Yes
- **Command**: `file check`



### directory: scripts
- **Status**: ✅ PASSED
- **Required**: Yes
- **Command**: `file check`



## Production Deployment Steps


### ✅ Ready for Deployment

1. **Environment Setup**
   ```bash
   # Copy environment template
   cp .env.example .env.production
   
   # Configure all required variables
   pnpm validate:env
   ```

2. **Build and Deploy**
   ```bash
   # Production build
   pnpm build:prod
   
   # Docker deployment
   docker-compose up -d
   
   # Or Vercel deployment
   vercel --prod
   ```

3. **Post-Deployment Verification**
   ```bash
   # Health check
   curl https://yourdomain.com/api/health
   
   # Database check
   curl https://yourdomain.com/api/health/db
   ```


## Monitoring and Maintenance

### Health Checks
- Application: `/api/health`
- Database: `/api/health/db`

### Log Monitoring
- Application logs: Check for errors and warnings
- Performance metrics: Monitor response times
- Security events: Monitor authentication failures

### Backup Verification
- Database backups: Verify automated backups are working
- File storage: Ensure user uploads are backed up
- Configuration: Keep environment variables backed up securely

## Support Information

- **Technical Documentation**: See PRODUCTION_DEPLOYMENT_GUIDE.md
- **Deployment Checklist**: See PRODUCTION_DEPLOYMENT_CHECKLIST.md
- **Code Quality Report**: See CODE_QUALITY_REPORT.md
- **Mobile Testing Report**: See MOBILE_RESPONSIVENESS_REPORT.md

---

*This report was generated automatically. Re-run `pnpm test:production` after making changes.*
