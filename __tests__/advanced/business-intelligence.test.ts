/**
 * Business Intelligence & Advanced Features Tests
 * Comprehensive testing of AI automation, analytics, and payment integrations
 */

import { businessIntelligence } from '../../src/lib/analytics/business-intelligence'
import { intelligentAutomation } from '../../src/lib/ai/intelligent-automation'
import { paymentGateway, PaymentMethod, PaymentStatus } from '../../src/lib/integrations/payment-gateways'

// Mock database and external dependencies
jest.mock('../../src/lib/database/performance', () => ({
  db: {
    query: jest.fn(),
    raw: jest.fn(),
  },
}))

jest.mock('../../src/lib/logger', () => ({
  logger: {
    info: jest.fn(),
    error: jest.fn(),
    warn: jest.fn(),
  },
}))

jest.mock('../../src/lib/monitoring', () => ({
  monitor: {
    timer: jest.fn(() => ({
      stop: jest.fn(),
    })),
    counter: jest.fn(),
  },
}))

describe('Business Intelligence Engine', () => {
  beforeEach(() => {
    jest.clearAllMocks()
  })

  describe('Business Metrics Calculation', () => {
    it('should calculate comprehensive business metrics', async () => {
      // Mock database responses
      const mockDb = require('../../src/lib/database/performance').db
      mockDb.query.mockResolvedValue({
        data: [{ total: 50000, count: 100 }],
      })

      const metrics = await businessIntelligence.getBusinessMetrics()

      expect(metrics).toBeDefined()
      expect(metrics.revenue).toBeDefined()
      expect(metrics.clients).toBeDefined()
      expect(metrics.appointments).toBeDefined()
      expect(metrics.treatments).toBeDefined()
      expect(metrics.staff).toBeDefined()

      // Verify revenue metrics structure
      expect(metrics.revenue).toHaveProperty('total')
      expect(metrics.revenue).toHaveProperty('monthly')
      expect(metrics.revenue).toHaveProperty('daily')
      expect(metrics.revenue).toHaveProperty('growth')
      expect(metrics.revenue).toHaveProperty('forecast')
    })

    it('should handle database errors gracefully', async () => {
      const mockDb = require('../../src/lib/database/performance').db
      mockDb.query.mockRejectedValue(new Error('Database connection failed'))

      await expect(businessIntelligence.getBusinessMetrics()).rejects.toThrow('Database connection failed')
    })

    it('should cache metrics for performance', async () => {
      const mockDb = require('../../src/lib/database/performance').db
      mockDb.query.mockResolvedValue({
        data: [{ total: 50000, count: 100 }],
      })

      // First call
      await businessIntelligence.getBusinessMetrics()
      
      // Second call should use cache
      await businessIntelligence.getBusinessMetrics()

      // Database should only be called once due to caching
      expect(mockDb.query).toHaveBeenCalledTimes(1)
    })

    it('should calculate revenue growth correctly', async () => {
      const mockDb = require('../../src/lib/database/performance').db
      mockDb.query
        .mockResolvedValueOnce({ data: [{ total: 45000 }] }) // Current month
        .mockResolvedValueOnce({ data: [{ total: 1500 }] })  // Daily
        .mockResolvedValueOnce({ data: [{ total: 40000 }] }) // Last month
        .mockResolvedValueOnce({ data: [{ total: 500000 }] }) // Total

      const metrics = await businessIntelligence.getBusinessMetrics()

      expect(metrics.revenue.growth).toBeCloseTo(12.5) // (45000 - 40000) / 40000 * 100
    })
  })

  describe('Predictive Analytics', () => {
    it('should generate predictive analytics', async () => {
      const analytics = await businessIntelligence.getPredictiveAnalytics()

      expect(analytics).toBeDefined()
      expect(analytics.client_behavior).toBeDefined()
      expect(analytics.revenue_forecast).toBeDefined()
      expect(analytics.appointment_demand).toBeDefined()

      // Verify client behavior predictions
      expect(analytics.client_behavior.churn_risk).toBeInstanceOf(Array)
      expect(analytics.client_behavior.next_treatment).toBeInstanceOf(Array)
      expect(analytics.client_behavior.lifetime_value).toBeInstanceOf(Array)
    })

    it('should provide confidence scores for predictions', async () => {
      const analytics = await businessIntelligence.getPredictiveAnalytics()

      expect(analytics.revenue_forecast.confidence_interval).toHaveLength(2)
      expect(analytics.revenue_forecast.confidence_interval[0]).toBeLessThan(
        analytics.revenue_forecast.confidence_interval[1]
      )
    })
  })

  describe('Revenue Forecasting', () => {
    it('should generate revenue forecast with trend analysis', async () => {
      const mockDb = require('../../src/lib/database/performance').db
      mockDb.query.mockResolvedValue({
        data: [
          { month: '2024-01', total: 40000 },
          { month: '2024-02', total: 42000 },
          { month: '2024-03', total: 45000 },
        ],
      })

      const metrics = await businessIntelligence.getBusinessMetrics()
      const forecast = metrics.revenue.forecast

      expect(forecast).toBeInstanceOf(Array)
      expect(forecast.length).toBe(6) // 6 months forecast
      expect(forecast.every(value => value >= 0)).toBe(true)
    })
  })
})

describe('Intelligent Automation Engine', () => {
  beforeEach(() => {
    jest.clearAllMocks()
  })

  describe('Scheduling Recommendations', () => {
    it('should generate intelligent scheduling recommendations', async () => {
      const recommendation = await intelligentAutomation.generateSchedulingRecommendation(
        'client_123',
        'treatment_456',
        new Date('2024-12-25T10:00:00Z')
      )

      expect(recommendation).toBeDefined()
      expect(recommendation.optimal_time).toBeInstanceOf(Date)
      expect(recommendation.alternative_times).toBeInstanceOf(Array)
      expect(recommendation.staff_recommendation).toBeDefined()
      expect(recommendation.room_recommendation).toBeDefined()
      expect(recommendation.confidence).toBeGreaterThan(0)
      expect(recommendation.confidence).toBeLessThanOrEqual(1)
      expect(recommendation.reasoning).toBeInstanceOf(Array)
    })

    it('should consider client preferences in scheduling', async () => {
      const recommendation = await intelligentAutomation.generateSchedulingRecommendation(
        'client_123',
        'treatment_456'
      )

      expect(recommendation.reasoning).toContain('基于客户历史偏好选择时间')
    })

    it('should provide alternative time slots', async () => {
      const recommendation = await intelligentAutomation.generateSchedulingRecommendation(
        'client_123',
        'treatment_456'
      )

      expect(recommendation.alternative_times.length).toBeGreaterThan(0)
      expect(recommendation.alternative_times.every(time => time instanceof Date)).toBe(true)
    })
  })

  describe('Treatment Recommendations', () => {
    it('should generate personalized treatment recommendations', async () => {
      const recommendations = await intelligentAutomation.generateTreatmentRecommendations(
        'client_123',
        3
      )

      expect(recommendations).toBeInstanceOf(Array)
      expect(recommendations.length).toBeLessThanOrEqual(3)
      
      recommendations.forEach(rec => {
        expect(rec.treatment_id).toBeDefined()
        expect(rec.treatment_name).toBeDefined()
        expect(rec.probability).toBeGreaterThan(0)
        expect(rec.probability).toBeLessThanOrEqual(1)
        expect(rec.reasoning).toBeInstanceOf(Array)
        expect(rec.expected_satisfaction).toBeGreaterThan(0)
        expect(rec.estimated_cost).toBeGreaterThan(0)
      })
    })

    it('should rank recommendations by probability', async () => {
      const recommendations = await intelligentAutomation.generateTreatmentRecommendations(
        'client_123',
        5
      )

      for (let i = 1; i < recommendations.length; i++) {
        expect(recommendations[i - 1].probability).toBeGreaterThanOrEqual(
          recommendations[i].probability
        )
      }
    })

    it('should provide reasoning for recommendations', async () => {
      const recommendations = await intelligentAutomation.generateTreatmentRecommendations(
        'client_123',
        1
      )

      expect(recommendations[0].reasoning.length).toBeGreaterThan(0)
      expect(recommendations[0].reasoning.every(reason => typeof reason === 'string')).toBe(true)
    })
  })

  describe('Pricing Optimization', () => {
    it('should optimize pricing based on market conditions', async () => {
      const optimization = await intelligentAutomation.optimizePricing('treatment_123')

      expect(optimization).toBeDefined()
      expect(optimization.current_price).toBeGreaterThan(0)
      expect(optimization.recommended_price).toBeGreaterThan(0)
      expect(optimization.price_change).toBeDefined()
      expect(optimization.expected_demand_change).toBeDefined()
      expect(optimization.revenue_impact).toBeDefined()
      expect(optimization.confidence).toBeGreaterThan(0)
      expect(optimization.confidence).toBeLessThanOrEqual(1)
      expect(optimization.market_factors).toBeInstanceOf(Array)
    })

    it('should consider competitor pricing', async () => {
      const optimization = await intelligentAutomation.optimizePricing('treatment_123')

      expect(optimization.market_factors).toContain('竞争对手定价')
    })
  })

  describe('Client Communication', () => {
    it('should generate personalized client communications', async () => {
      const communication = await intelligentAutomation.generateClientCommunication(
        'client_123',
        'reminder'
      )

      expect(communication).toBeDefined()
      expect(communication.subject).toBeDefined()
      expect(communication.message).toBeDefined()
      expect(['sms', 'email', 'wechat']).toContain(communication.channel)
      expect(communication.timing).toBeInstanceOf(Date)
      expect(communication.personalization).toBeDefined()
    })

    it('should adapt message based on communication type', async () => {
      const reminder = await intelligentAutomation.generateClientCommunication(
        'client_123',
        'reminder'
      )
      
      const followUp = await intelligentAutomation.generateClientCommunication(
        'client_123',
        'follow_up'
      )

      expect(reminder.subject).not.toBe(followUp.subject)
      expect(reminder.message).not.toBe(followUp.message)
    })
  })

  describe('Churn Prediction', () => {
    it('should predict client churn risk', async () => {
      const prediction = await intelligentAutomation.predictChurnRisk('client_123')

      expect(prediction).toBeDefined()
      expect(prediction.risk_score).toBeGreaterThanOrEqual(0)
      expect(prediction.risk_score).toBeLessThanOrEqual(1)
      expect(['low', 'medium', 'high']).toContain(prediction.risk_level)
      expect(prediction.factors).toBeInstanceOf(Array)
      expect(prediction.recommendations).toBeInstanceOf(Array)
      expect(prediction.confidence).toBeGreaterThan(0)
      expect(prediction.confidence).toBeLessThanOrEqual(1)
    })

    it('should provide actionable recommendations for high-risk clients', async () => {
      const prediction = await intelligentAutomation.predictChurnRisk('client_123')

      if (prediction.risk_level === 'high') {
        expect(prediction.recommendations.length).toBeGreaterThan(0)
        expect(prediction.recommendations.every(rec => typeof rec === 'string')).toBe(true)
      }
    })
  })

  describe('Treatment Effectiveness Analysis', () => {
    it('should analyze treatment effectiveness', async () => {
      const analysis = await intelligentAutomation.analyzeTreatmentEffectiveness('treatment_123')

      expect(analysis).toBeDefined()
      expect(analysis.effectiveness_score).toBeGreaterThanOrEqual(0)
      expect(analysis.effectiveness_score).toBeLessThanOrEqual(100)
      expect(analysis.satisfaction_prediction).toBeGreaterThan(0)
      expect(analysis.satisfaction_prediction).toBeLessThanOrEqual(5)
      expect(analysis.success_factors).toBeInstanceOf(Array)
      expect(analysis.improvement_suggestions).toBeInstanceOf(Array)
      expect(analysis.comparative_analysis).toBeDefined()
    })

    it('should provide comparative analysis', async () => {
      const analysis = await intelligentAutomation.analyzeTreatmentEffectiveness('treatment_123')

      expect(analysis.comparative_analysis.vs_average).toBeDefined()
      expect(analysis.comparative_analysis.vs_similar_treatments).toBeDefined()
    })
  })
})

describe('Payment Gateway Integration', () => {
  beforeEach(() => {
    jest.clearAllMocks()
  })

  describe('Payment Processing', () => {
    it('should process Alipay payments', async () => {
      const paymentRequest = {
        amount: 1000,
        currency: 'CNY',
        method: PaymentMethod.ALIPAY,
        description: '面部护理治疗',
        client_id: 'client_123',
        appointment_id: 'appointment_456',
      }

      const response = await paymentGateway.processPayment(paymentRequest)

      expect(response).toBeDefined()
      expect(response.payment_id).toBeDefined()
      expect(response.status).toBe(PaymentStatus.PENDING)
      expect(response.amount).toBe(1000)
      expect(response.currency).toBe('CNY')
      expect(response.method).toBe(PaymentMethod.ALIPAY)
      expect(response.qr_code).toBeDefined()
      expect(response.deep_link).toBeDefined()
      expect(response.expires_at).toBeInstanceOf(Date)
    })

    it('should process WeChat Pay payments', async () => {
      const paymentRequest = {
        amount: 1500,
        currency: 'CNY',
        method: PaymentMethod.WECHAT_PAY,
        description: '激光治疗',
        client_id: 'client_123',
      }

      const response = await paymentGateway.processPayment(paymentRequest)

      expect(response.method).toBe(PaymentMethod.WECHAT_PAY)
      expect(response.qr_code).toBeDefined()
      expect(response.deep_link).toContain('weixin://')
    })

    it('should process cash payments', async () => {
      const paymentRequest = {
        amount: 800,
        currency: 'CNY',
        method: PaymentMethod.CASH,
        description: '现金支付',
        client_id: 'client_123',
      }

      const response = await paymentGateway.processPayment(paymentRequest)

      expect(response.method).toBe(PaymentMethod.CASH)
      expect(response.status).toBe(PaymentStatus.COMPLETED)
    })

    it('should validate payment requests', async () => {
      const invalidRequest = {
        amount: -100, // Invalid amount
        currency: 'CNY',
        method: PaymentMethod.ALIPAY,
        description: 'Test payment',
        client_id: 'client_123',
      }

      await expect(paymentGateway.processPayment(invalidRequest)).rejects.toThrow(
        'Payment amount must be greater than 0'
      )
    })

    it('should handle unsupported payment methods', async () => {
      const paymentRequest = {
        amount: 1000,
        currency: 'CNY',
        method: 'unsupported_method' as PaymentMethod,
        description: 'Test payment',
        client_id: 'client_123',
      }

      await expect(paymentGateway.processPayment(paymentRequest)).rejects.toThrow(
        'Unsupported payment method'
      )
    })
  })

  describe('Payment Status Checking', () => {
    it('should check payment status', async () => {
      const status = await paymentGateway.checkPaymentStatus(
        'payment_123',
        PaymentMethod.ALIPAY
      )

      expect(status).toBeDefined()
      expect(status.payment_id).toBe('payment_123')
      expect(status.status).toBeDefined()
      expect(status.method).toBe(PaymentMethod.ALIPAY)
    })
  })

  describe('Refund Processing', () => {
    it('should process full refunds', async () => {
      const refundRequest = {
        payment_id: 'payment_123',
        reason: '客户取消预约',
      }

      const refund = await paymentGateway.processRefund(refundRequest)

      expect(refund).toBeDefined()
      expect(refund.refund_id).toBeDefined()
      expect(refund.status).toBe(PaymentStatus.COMPLETED)
      expect(refund.amount).toBeGreaterThan(0)
      expect(refund.processed_at).toBeInstanceOf(Date)
    })

    it('should process partial refunds', async () => {
      const refundRequest = {
        payment_id: 'payment_123',
        amount: 500, // Partial refund
        reason: '部分服务取消',
      }

      const refund = await paymentGateway.processRefund(refundRequest)

      expect(refund.amount).toBe(500)
    })
  })

  describe('Supported Payment Methods', () => {
    it('should return all supported payment methods', () => {
      const methods = paymentGateway.getSupportedMethods()

      expect(methods).toContain(PaymentMethod.ALIPAY)
      expect(methods).toContain(PaymentMethod.WECHAT_PAY)
      expect(methods).toContain(PaymentMethod.UNION_PAY)
      expect(methods).toContain(PaymentMethod.CREDIT_CARD)
      expect(methods).toContain(PaymentMethod.CASH)
      expect(methods).toContain(PaymentMethod.BANK_TRANSFER)
    })
  })

  describe('Payment Security', () => {
    it('should log payment operations for audit', async () => {
      const paymentRequest = {
        amount: 1000,
        currency: 'CNY',
        method: PaymentMethod.ALIPAY,
        description: 'Test payment',
        client_id: 'client_123',
      }

      await paymentGateway.processPayment(paymentRequest)

      // Verify that audit logging was called
      // This would be verified through mocks in a real implementation
    })

    it('should handle payment processing errors gracefully', async () => {
      // Mock a payment gateway error
      const paymentRequest = {
        amount: 1000,
        currency: 'CNY',
        method: PaymentMethod.ALIPAY,
        description: 'Test payment',
        client_id: 'client_123',
      }

      // In a real test, we would mock the gateway to throw an error
      // and verify proper error handling
    })
  })

  describe('Performance and Scalability', () => {
    it('should handle concurrent payment requests', async () => {
      const paymentRequests = Array.from({ length: 10 }, (_, i) => ({
        amount: 1000 + i * 100,
        currency: 'CNY',
        method: PaymentMethod.ALIPAY,
        description: `Concurrent payment ${i}`,
        client_id: `client_${i}`,
      }))

      const promises = paymentRequests.map(request => 
        paymentGateway.processPayment(request)
      )

      const responses = await Promise.all(promises)

      expect(responses).toHaveLength(10)
      responses.forEach((response, index) => {
        expect(response.amount).toBe(1000 + index * 100)
        expect(response.payment_id).toBeDefined()
      })
    })

    it('should process payments within acceptable time limits', async () => {
      const paymentRequest = {
        amount: 1000,
        currency: 'CNY',
        method: PaymentMethod.ALIPAY,
        description: 'Performance test',
        client_id: 'client_123',
      }

      const startTime = Date.now()
      await paymentGateway.processPayment(paymentRequest)
      const endTime = Date.now()

      const processingTime = endTime - startTime
      expect(processingTime).toBeLessThan(5000) // Should complete within 5 seconds
    })
  })
})
