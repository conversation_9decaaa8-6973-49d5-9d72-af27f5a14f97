-- Medical Aesthetics CRM Database Schema Validation
-- This script validates the database schema integrity, indexes, and RLS policies

-- =================================================================
-- 1. SCHEMA VALIDATION
-- =================================================================

-- Check if all required tables exist
SELECT 
  table_name,
  table_type
FROM information_schema.tables 
WHERE table_schema = 'public' 
  AND table_name IN (
    'clients', 'treatments', 'appointments', 'invoices', 
    'invoice_items', 'payments', 'contact_logs'
  )
ORDER BY table_name;

-- =================================================================
-- 2. FOR<PERSON><PERSON><PERSON> KEY CONSTRAINTS VALIDATION
-- =================================================================

-- Check foreign key constraints
SELECT 
  tc.table_name,
  tc.constraint_name,
  tc.constraint_type,
  kcu.column_name,
  ccu.table_name AS foreign_table_name,
  ccu.column_name AS foreign_column_name
FROM information_schema.table_constraints AS tc
JOIN information_schema.key_column_usage AS kcu
  ON tc.constraint_name = kcu.constraint_name
  AND tc.table_schema = kcu.table_schema
JOIN information_schema.constraint_column_usage AS ccu
  ON ccu.constraint_name = tc.constraint_name
  AND ccu.table_schema = tc.table_schema
WHERE tc.constraint_type = 'FOREIGN KEY'
  AND tc.table_schema = 'public'
ORDER BY tc.table_name, tc.constraint_name;

-- =================================================================
-- 3. INDEX VALIDATION
-- =================================================================

-- Check existing indexes
SELECT 
  schemaname,
  tablename,
  indexname,
  indexdef
FROM pg_indexes 
WHERE schemaname = 'public'
  AND tablename IN (
    'clients', 'treatments', 'appointments', 'invoices', 
    'invoice_items', 'payments', 'contact_logs'
  )
ORDER BY tablename, indexname;

-- =================================================================
-- 4. RECOMMENDED INDEXES FOR PERFORMANCE
-- =================================================================

-- Create indexes for better query performance
-- (Run these if they don't exist)

-- Clients table indexes
CREATE INDEX IF NOT EXISTS idx_clients_phone ON clients(phone);
CREATE INDEX IF NOT EXISTS idx_clients_email ON clients(email);
CREATE INDEX IF NOT EXISTS idx_clients_status ON clients(status);
CREATE INDEX IF NOT EXISTS idx_clients_name ON clients(first_name, last_name);

-- Appointments table indexes
CREATE INDEX IF NOT EXISTS idx_appointments_client_id ON appointments(client_id);
CREATE INDEX IF NOT EXISTS idx_appointments_treatment_id ON appointments(treatment_id);
CREATE INDEX IF NOT EXISTS idx_appointments_date ON appointments(appointment_date);
CREATE INDEX IF NOT EXISTS idx_appointments_status ON appointments(status);
CREATE INDEX IF NOT EXISTS idx_appointments_date_time ON appointments(appointment_date, start_time);

-- Invoices table indexes
CREATE INDEX IF NOT EXISTS idx_invoices_client_id ON invoices(client_id);
CREATE INDEX IF NOT EXISTS idx_invoices_number ON invoices(invoice_number);
CREATE INDEX IF NOT EXISTS idx_invoices_date ON invoices(invoice_date);
CREATE INDEX IF NOT EXISTS idx_invoices_status ON invoices(status);
CREATE INDEX IF NOT EXISTS idx_invoices_due_date ON invoices(due_date);

-- Invoice items table indexes
CREATE INDEX IF NOT EXISTS idx_invoice_items_invoice_id ON invoice_items(invoice_id);
CREATE INDEX IF NOT EXISTS idx_invoice_items_appointment_id ON invoice_items(appointment_id);

-- Payments table indexes
CREATE INDEX IF NOT EXISTS idx_payments_invoice_id ON payments(invoice_id);
CREATE INDEX IF NOT EXISTS idx_payments_client_id ON payments(client_id);
CREATE INDEX IF NOT EXISTS idx_payments_date ON payments(payment_date);
CREATE INDEX IF NOT EXISTS idx_payments_method ON payments(payment_method);

-- Contact logs table indexes
CREATE INDEX IF NOT EXISTS idx_contact_logs_client_id ON contact_logs(client_id);
CREATE INDEX IF NOT EXISTS idx_contact_logs_date ON contact_logs(created_at);
CREATE INDEX IF NOT EXISTS idx_contact_logs_follow_up ON contact_logs(follow_up_required, follow_up_date);

-- Treatments table indexes
CREATE INDEX IF NOT EXISTS idx_treatments_category ON treatments(category);
CREATE INDEX IF NOT EXISTS idx_treatments_active ON treatments(is_active);

-- =================================================================
-- 5. ROW LEVEL SECURITY (RLS) VALIDATION
-- =================================================================

-- Check RLS status for all tables
SELECT 
  schemaname,
  tablename,
  rowsecurity
FROM pg_tables 
WHERE schemaname = 'public'
  AND tablename IN (
    'clients', 'treatments', 'appointments', 'invoices', 
    'invoice_items', 'payments', 'contact_logs'
  );

-- Check existing RLS policies
SELECT 
  schemaname,
  tablename,
  policyname,
  permissive,
  roles,
  cmd,
  qual,
  with_check
FROM pg_policies 
WHERE schemaname = 'public'
ORDER BY tablename, policyname;

-- =================================================================
-- 6. DATA INTEGRITY CHECKS
-- =================================================================

-- Check for orphaned records
-- Appointments without valid clients
SELECT COUNT(*) as orphaned_appointments
FROM appointments a
LEFT JOIN clients c ON a.client_id = c.id
WHERE c.id IS NULL;

-- Appointments without valid treatments
SELECT COUNT(*) as orphaned_appointment_treatments
FROM appointments a
LEFT JOIN treatments t ON a.treatment_id = t.id
WHERE t.id IS NULL;

-- Invoice items without valid invoices
SELECT COUNT(*) as orphaned_invoice_items
FROM invoice_items ii
LEFT JOIN invoices i ON ii.invoice_id = i.id
WHERE i.id IS NULL;

-- Payments without valid invoices
SELECT COUNT(*) as orphaned_payments
FROM payments p
LEFT JOIN invoices i ON p.invoice_id = i.id
WHERE i.id IS NULL;

-- Contact logs without valid clients
SELECT COUNT(*) as orphaned_contact_logs
FROM contact_logs cl
LEFT JOIN clients c ON cl.client_id = c.id
WHERE c.id IS NULL;

-- =================================================================
-- 7. BUSINESS RULE VALIDATION
-- =================================================================

-- Check for negative amounts
SELECT 'treatments' as table_name, COUNT(*) as negative_amounts
FROM treatments 
WHERE default_price < 0 OR fixed_deposit_amount < 0 OR consultation_fee < 0
UNION ALL
SELECT 'invoices', COUNT(*)
FROM invoices 
WHERE total_amount < 0 OR deposit_amount < 0
UNION ALL
SELECT 'payments', COUNT(*)
FROM payments 
WHERE amount < 0;

-- Check for invalid appointment times (end time before start time)
SELECT COUNT(*) as invalid_appointment_times
FROM appointments 
WHERE end_time <= start_time;

-- Check for future payment dates
SELECT COUNT(*) as future_payments
FROM payments 
WHERE payment_date > CURRENT_DATE;
