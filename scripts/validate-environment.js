#!/usr/bin/env node

/**
 * Environment Configuration Validator
 * Validates all required environment variables for production deployment
 */

const fs = require('fs')
const path = require('path')

// Color codes for console output
const colors = {
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m',
  white: '\x1b[37m',
  reset: '\x1b[0m',
  bold: '\x1b[1m'
}

// Environment variable definitions
const envVars = {
  // Core Application
  NODE_ENV: {
    required: true,
    description: 'Application environment',
    validValues: ['development', 'production', 'test'],
    example: 'production'
  },
  NEXT_PUBLIC_APP_URL: {
    required: true,
    description: 'Public application URL',
    example: 'https://yourdomain.com'
  },

  // Database (Supabase)
  NEXT_PUBLIC_SUPABASE_URL: {
    required: true,
    description: 'Supabase project URL',
    example: 'https://your-project.supabase.co'
  },
  NEXT_PUBLIC_SUPABASE_ANON_KEY: {
    required: true,
    description: 'Supabase anonymous key',
    example: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...'
  },
  SUPABASE_SERVICE_ROLE_KEY: {
    required: true,
    description: 'Supabase service role key (server-side only)',
    example: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...'
  },

  // Authentication (Clerk)
  NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY: {
    required: false,
    description: 'Clerk publishable key (optional for keyless mode)',
    example: 'pk_test_...'
  },
  CLERK_SECRET_KEY: {
    required: false,
    description: 'Clerk secret key (optional for keyless mode)',
    example: 'sk_test_...'
  },

  // Security
  NEXTAUTH_SECRET: {
    required: true,
    description: 'NextAuth secret for JWT signing',
    example: 'your-32-character-secret-here'
  },
  ENCRYPTION_KEY: {
    required: true,
    description: '32-character encryption key',
    example: 'your-32-character-encryption-key'
  },
  JWT_SECRET: {
    required: true,
    description: 'JWT secret for token signing',
    example: 'your-jwt-secret-here'
  },

  // Error Tracking (Sentry)
  NEXT_PUBLIC_SENTRY_DSN: {
    required: false,
    description: 'Sentry DSN for error tracking',
    example: 'https://****@****.ingest.sentry.io/****'
  },
  NEXT_PUBLIC_SENTRY_ORG: {
    required: false,
    description: 'Sentry organization name',
    example: 'your-org'
  },
  NEXT_PUBLIC_SENTRY_PROJECT: {
    required: false,
    description: 'Sentry project name',
    example: 'your-project'
  },
  SENTRY_AUTH_TOKEN: {
    required: false,
    description: 'Sentry auth token for source maps',
    example: 'sntrys_****'
  },

  // Email Configuration
  SMTP_HOST: {
    required: false,
    description: 'SMTP server host',
    example: 'smtp.gmail.com'
  },
  SMTP_PORT: {
    required: false,
    description: 'SMTP server port',
    example: '587'
  },
  SMTP_USER: {
    required: false,
    description: 'SMTP username',
    example: '<EMAIL>'
  },
  SMTP_PASS: {
    required: false,
    description: 'SMTP password',
    example: 'your-app-password'
  },

  // Cache Configuration
  REDIS_URL: {
    required: false,
    description: 'Redis connection URL',
    example: 'redis://localhost:6379'
  },

  // Monitoring
  LOG_LEVEL: {
    required: false,
    description: 'Application log level',
    validValues: ['debug', 'info', 'warn', 'error'],
    example: 'info'
  }
}

function validateEnvironment() {
  console.log(`${colors.bold}${colors.cyan}🔍 Environment Configuration Validator${colors.reset}\n`)

  const errors = []
  const warnings = []
  const info = []

  // Check if .env files exist
  const envFiles = ['.env', '.env.local', '.env.production', '.env.production.local']
  const existingEnvFiles = envFiles.filter(file => fs.existsSync(file))

  if (existingEnvFiles.length === 0) {
    warnings.push('No environment files found. Create .env.local for development or .env.production for production.')
  } else {
    info.push(`Found environment files: ${existingEnvFiles.join(', ')}`)
  }

  // Validate each environment variable
  Object.entries(envVars).forEach(([key, config]) => {
    const value = process.env[key]

    if (config.required && !value) {
      errors.push(`❌ ${key}: Required but not set. ${config.description}`)
    } else if (!value) {
      warnings.push(`⚠️  ${key}: Optional but not set. ${config.description}`)
    } else {
      // Validate value format
      if (config.validValues && !config.validValues.includes(value)) {
        errors.push(`❌ ${key}: Invalid value "${value}". Must be one of: ${config.validValues.join(', ')}`)
      } else {
        info.push(`✅ ${key}: Set`)
      }

      // Additional validations
      if (key.includes('URL') && !value.startsWith('http')) {
        warnings.push(`⚠️  ${key}: Should start with http:// or https://`)
      }

      if (key.includes('SECRET') || key.includes('KEY')) {
        if (value.length < 16) {
          warnings.push(`⚠️  ${key}: Should be at least 16 characters long for security`)
        }
      }
    }
  })

  // Display results
  if (info.length > 0) {
    console.log(`${colors.green}${colors.bold}✅ Configured Variables:${colors.reset}`)
    info.forEach(msg => console.log(`  ${colors.green}${msg}${colors.reset}`))
    console.log()
  }

  if (warnings.length > 0) {
    console.log(`${colors.yellow}${colors.bold}⚠️  Warnings:${colors.reset}`)
    warnings.forEach(msg => console.log(`  ${colors.yellow}${msg}${colors.reset}`))
    console.log()
  }

  if (errors.length > 0) {
    console.log(`${colors.red}${colors.bold}❌ Errors:${colors.reset}`)
    errors.forEach(msg => console.log(`  ${colors.red}${msg}${colors.reset}`))
    console.log()
  }

  // Summary
  console.log(`${colors.bold}📊 Summary:${colors.reset}`)
  console.log(`  ${colors.green}✅ Configured: ${info.length}${colors.reset}`)
  console.log(`  ${colors.yellow}⚠️  Warnings: ${warnings.length}${colors.reset}`)
  console.log(`  ${colors.red}❌ Errors: ${errors.length}${colors.reset}`)

  if (errors.length === 0) {
    console.log(`\n${colors.green}${colors.bold}🎉 Environment validation passed!${colors.reset}`)
    
    if (warnings.length > 0) {
      console.log(`${colors.yellow}Note: There are ${warnings.length} warnings that should be addressed for optimal configuration.${colors.reset}`)
    }
  } else {
    console.log(`\n${colors.red}${colors.bold}💥 Environment validation failed!${colors.reset}`)
    console.log(`${colors.red}Please fix the ${errors.length} error(s) above before deploying to production.${colors.reset}`)
  }

  // Environment-specific recommendations
  const nodeEnv = process.env.NODE_ENV
  if (nodeEnv === 'production') {
    console.log(`\n${colors.blue}${colors.bold}🚀 Production Environment Recommendations:${colors.reset}`)
    console.log(`  ${colors.blue}• Ensure all secrets are properly secured${colors.reset}`)
    console.log(`  ${colors.blue}• Enable error tracking (Sentry)${colors.reset}`)
    console.log(`  ${colors.blue}• Configure email notifications${colors.reset}`)
    console.log(`  ${colors.blue}• Set up monitoring and logging${colors.reset}`)
    console.log(`  ${colors.blue}• Enable Redis caching for better performance${colors.reset}`)
  }

  return errors.length === 0
}

function generateEnvExample() {
  console.log(`\n${colors.cyan}${colors.bold}📝 Generating .env.example...${colors.reset}`)
  
  let content = '# Medical Aesthetics CRM - Environment Variables\n'
  content += '# Copy this file to .env.local for development or .env.production for production\n\n'

  Object.entries(envVars).forEach(([key, config]) => {
    content += `# ${config.description}\n`
    if (config.validValues) {
      content += `# Valid values: ${config.validValues.join(', ')}\n`
    }
    content += `${key}=${config.example}\n\n`
  })

  fs.writeFileSync('.env.example', content)
  console.log(`${colors.green}✅ .env.example generated successfully!${colors.reset}`)
}

// Main execution
if (require.main === module) {
  const args = process.argv.slice(2)
  
  if (args.includes('--generate-example')) {
    generateEnvExample()
  } else {
    const isValid = validateEnvironment()
    process.exit(isValid ? 0 : 1)
  }
}

module.exports = { validateEnvironment, generateEnvExample }
