#!/usr/bin/env node

/**
 * Automatic Hydration Issues Fixer
 * Automatically fixes common hydration patterns in React/Next.js applications
 */

const fs = require('fs')
const path = require('path')

// Color codes for console output
const colors = {
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m',
  white: '\x1b[37m',
  reset: '\x1b[0m',
  bold: '\x1b[1m'
}

function log(message, color = 'white') {
  console.log(`${colors[color]}${message}${colors.reset}`)
}

// Auto-fix patterns
const autoFixPatterns = [
  {
    name: 'Window object usage',
    pattern: /(?<!typeof\s+)window\./g,
    replacement: (match, content, filePath) => {
      // Skip if already has typeof check
      if (content.includes('typeof window !== "undefined"')) {
        return content
      }
      return content.replace(match, 'typeof window !== "undefined" && window.')
    },
    description: 'Add typeof window check'
  },
  {
    name: 'Document object usage',
    pattern: /(?<!typeof\s+)document\./g,
    replacement: (match, content, filePath) => {
      // Skip if already has typeof check
      if (content.includes('typeof document !== "undefined"')) {
        return content
      }
      return content.replace(match, 'typeof document !== "undefined" && document.')
    },
    description: 'Add typeof document check'
  },
  {
    name: 'LocalStorage usage',
    pattern: /localStorage\./g,
    replacement: (match, content, filePath) => {
      // Skip if already has typeof check
      if (content.includes('typeof window !== "undefined"')) {
        return content
      }
      return content.replace(match, 'typeof window !== "undefined" && localStorage.')
    },
    description: 'Add typeof window check for localStorage'
  },
  {
    name: 'SessionStorage usage',
    pattern: /sessionStorage\./g,
    replacement: (match, content, filePath) => {
      // Skip if already has typeof check
      if (content.includes('typeof window !== "undefined"')) {
        return content
      }
      return content.replace(match, 'typeof window !== "undefined" && sessionStorage.')
    },
    description: 'Add typeof window check for sessionStorage'
  },
  {
    name: 'toLocaleDateString without options',
    pattern: /\.toLocaleDateString\(\s*['"]zh-CN['"]\s*\)/g,
    replacement: (match, content, filePath) => {
      return content.replace(match, `.toLocaleDateString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        timeZone: 'Asia/Shanghai'
      })`)
    },
    description: 'Add explicit options to toLocaleDateString'
  }
]

function applyAutoFixes(filePath) {
  try {
    let content = fs.readFileSync(filePath, 'utf8')
    let modified = false
    const appliedFixes = []

    autoFixPatterns.forEach(pattern => {
      const originalContent = content
      content = pattern.replacement(pattern.pattern, content, filePath)
      
      if (content !== originalContent) {
        modified = true
        appliedFixes.push(pattern.name)
      }
    })

    if (modified) {
      fs.writeFileSync(filePath, content)
      log(`✅ Fixed ${filePath}`, 'green')
      appliedFixes.forEach(fix => {
        log(`   - ${fix}`, 'cyan')
      })
      return appliedFixes.length
    }

    return 0
  } catch (error) {
    log(`❌ Error fixing ${filePath}: ${error.message}`, 'red')
    return 0
  }
}

function processDirectory(dir, extensions = ['.tsx', '.ts', '.jsx', '.js']) {
  let totalFixes = 0
  let filesProcessed = 0

  function processRecursive(currentDir) {
    try {
      const files = fs.readdirSync(currentDir, { withFileTypes: true })
      
      for (const file of files) {
        const fullPath = path.join(currentDir, file.name)
        
        if (file.isDirectory() && !file.name.startsWith('.') && file.name !== 'node_modules') {
          processRecursive(fullPath)
        } else if (file.isFile() && extensions.some(ext => file.name.endsWith(ext))) {
          // Skip API routes and server-side files
          if (fullPath.includes('/api/') || fullPath.includes('route.ts') || fullPath.includes('layout.tsx')) {
            continue
          }
          
          filesProcessed++
          const fixes = applyAutoFixes(fullPath)
          totalFixes += fixes
        }
      }
    } catch (error) {
      // Directory might not exist or be accessible
    }
  }

  processRecursive(dir)
  return { totalFixes, filesProcessed }
}

function createHydrationGuardTemplate() {
  const template = `// Hydration Guard Hook
import { useState, useEffect } from 'react'

export function useHydrationGuard() {
  const [mounted, setMounted] = useState(false)

  useEffect(() => {
    setMounted(true)
  }, [])

  return mounted
}

// Usage example:
// const mounted = useHydrationGuard()
// if (!mounted) return null // or loading state
// // Client-side only code here
`

  const hookPath = 'src/hooks/use-hydration-guard.ts'
  
  try {
    // Create hooks directory if it doesn't exist
    const hooksDir = path.dirname(hookPath)
    if (!fs.existsSync(hooksDir)) {
      fs.mkdirSync(hooksDir, { recursive: true })
    }
    
    // Only create if it doesn't exist
    if (!fs.existsSync(hookPath)) {
      fs.writeFileSync(hookPath, template)
      log(`✅ Created hydration guard hook: ${hookPath}`, 'green')
      return true
    }
  } catch (error) {
    log(`⚠️ Could not create hydration guard hook: ${error.message}`, 'yellow')
  }
  
  return false
}

function generateFixReport(results) {
  const report = `# Hydration Auto-Fix Report

Generated on: ${new Date().toISOString()}

## Summary

- **Files Processed**: ${results.filesProcessed}
- **Total Fixes Applied**: ${results.totalFixes}

## Auto-Fix Patterns Applied

${autoFixPatterns.map(pattern => `
### ${pattern.name}
- **Description**: ${pattern.description}
- **Pattern**: \`${pattern.pattern.source}\`
`).join('\n')}

## Manual Fixes Still Needed

The following issues require manual intervention:

### 1. Math.random() Usage
Replace with deterministic calculations:
\`\`\`tsx
// ❌ Bad
const growth = Math.round(Math.random() * 20)

// ✅ Good
const growth = Math.min(Math.max(Math.floor((data.length * 0.1) + 5), 0), 50)
\`\`\`

### 2. new Date() Without Parameters
Use static dates or client-side only calculations:
\`\`\`tsx
// ❌ Bad
const now = new Date()

// ✅ Good - move to useEffect
useEffect(() => {
  const now = new Date()
  // Use now here
}, [])
\`\`\`

### 3. Dynamic Class Names
Ensure consistent class name generation:
\`\`\`tsx
// ❌ Bad
className={condition ? 'class-a' : 'class-b'}

// ✅ Good - use hydration guard
const mounted = useHydrationGuard()
className={mounted && condition ? 'class-a' : 'class-b'}
\`\`\`

## Next Steps

1. Run \`pnpm build\` to test for remaining hydration issues
2. Check browser console for hydration warnings
3. Use the hydration guard hook for client-side only code
4. Test on actual devices to ensure smooth hydration

---

*Run \`pnpm auto-fix:hydration\` to apply these fixes again after making changes.*
`

  return report
}

function main() {
  log('🔧 Auto-Fixing Hydration Issues', 'bold')
  
  // Create hydration guard hook
  createHydrationGuardTemplate()
  
  // Process source directories
  log('\n📁 Processing source files...', 'cyan')
  const srcResults = processDirectory('./src')
  
  log('\n📊 Results:', 'cyan')
  log(`Files processed: ${srcResults.filesProcessed}`)
  log(`Total fixes applied: ${srcResults.totalFixes}`, srcResults.totalFixes > 0 ? 'green' : 'yellow')
  
  // Generate report
  const report = generateFixReport(srcResults)
  fs.writeFileSync('HYDRATION_AUTO_FIX_REPORT.md', report)
  
  log('\n📝 Auto-fix report generated: HYDRATION_AUTO_FIX_REPORT.md', 'green')
  
  if (srcResults.totalFixes > 0) {
    log(`\n🎉 Applied ${srcResults.totalFixes} automatic fixes!`, 'green')
    log('⚠️ Some issues may still require manual fixes. Check the report for details.', 'yellow')
  } else {
    log('\n✅ No automatic fixes needed or all issues already fixed!', 'green')
  }
  
  log('\n📖 Next steps:', 'cyan')
  log('1. Run `pnpm build` to test for remaining issues')
  log('2. Check browser console for hydration warnings')
  log('3. Use the hydration guard hook for client-side only code')
}

if (require.main === module) {
  main()
}

module.exports = { applyAutoFixes, processDirectory, autoFixPatterns }
