#!/usr/bin/env node

/**
 * Mobile Responsiveness Test Script
 * Tests responsive design patterns and mobile-specific functionality
 */

const fs = require('fs')
const path = require('path')

// Color codes for console output
const colors = {
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m',
  white: '\x1b[37m',
  reset: '\x1b[0m',
  bold: '\x1b[1m'
}

function log(message, color = 'white') {
  console.log(`${colors[color]}${message}${colors.reset}`)
}

// Test configurations for different screen sizes
const viewports = [
  { name: 'Mobile Portrait', width: 375, height: 667, type: 'mobile' },
  { name: 'Mobile Landscape', width: 667, height: 375, type: 'mobile' },
  { name: 'Tablet Portrait', width: 768, height: 1024, type: 'tablet' },
  { name: 'Tablet Landscape', width: 1024, height: 768, type: 'tablet' },
  { name: 'Desktop Small', width: 1280, height: 720, type: 'desktop' },
  { name: 'Desktop Large', width: 1920, height: 1080, type: 'desktop' }
]

// Pages to test
const testPages = [
  '/dashboard/overview',
  '/dashboard/clients',
  '/dashboard/appointments',
  '/dashboard/calendar',
  '/dashboard/treatments',
  '/dashboard/invoices',
  '/dashboard/payments',
  '/dashboard/settings'
]

// Mobile-specific features to check
const mobileFeatures = {
  touchTargets: {
    description: 'Touch targets should be at least 44px',
    selector: 'button, a, input, [role="button"]',
    minSize: 44
  },
  textReadability: {
    description: 'Text should be at least 16px on mobile',
    selector: 'p, span, div, label',
    minSize: 16
  },
  horizontalScroll: {
    description: 'No horizontal scrolling on mobile',
    maxWidth: 375
  },
  mobileNavigation: {
    description: 'Mobile navigation should be accessible',
    selector: '[data-mobile-nav], .mobile-nav, .hamburger-menu'
  },
  formElements: {
    description: 'Form elements should be mobile-friendly',
    selector: 'input, textarea, select',
    minHeight: 44
  }
}

function analyzeResponsiveCSS() {
  log('\n📱 Analyzing Responsive CSS Patterns...', 'cyan')
  
  const cssFiles = []
  const issues = []
  const recommendations = []

  // Find CSS files
  function findCSSFiles(dir) {
    const files = fs.readdirSync(dir, { withFileTypes: true })
    
    for (const file of files) {
      const fullPath = path.join(dir, file.name)
      
      if (file.isDirectory() && !file.name.startsWith('.') && file.name !== 'node_modules') {
        findCSSFiles(fullPath)
      } else if (file.name.endsWith('.css') || file.name.endsWith('.scss')) {
        cssFiles.push(fullPath)
      }
    }
  }

  try {
    findCSSFiles('./src')
    findCSSFiles('./styles')
  } catch (error) {
    // Directories might not exist
  }

  // Analyze CSS files
  cssFiles.forEach(file => {
    try {
      const content = fs.readFileSync(file, 'utf8')
      
      // Check for responsive patterns
      const hasMediaQueries = /@media/.test(content)
      const hasFlexbox = /display:\s*flex/.test(content)
      const hasGrid = /display:\s*grid/.test(content)
      const hasResponsiveUnits = /(rem|em|vh|vw|%)/.test(content)
      
      if (!hasMediaQueries) {
        issues.push(`${file}: No media queries found`)
      }
      
      if (!hasResponsiveUnits) {
        recommendations.push(`${file}: Consider using responsive units (rem, em, %, vh, vw)`)
      }
      
      if (hasFlexbox || hasGrid) {
        log(`✅ ${file}: Uses modern layout (${hasFlexbox ? 'Flexbox' : ''}${hasGrid ? ' Grid' : ''})`, 'green')
      }
      
    } catch (error) {
      issues.push(`${file}: Could not read file`)
    }
  })

  return { cssFiles, issues, recommendations }
}

function analyzeComponentStructure() {
  log('\n🧩 Analyzing Component Structure...', 'cyan')
  
  const components = []
  const mobileComponents = []
  const issues = []

  function findComponents(dir) {
    try {
      const files = fs.readdirSync(dir, { withFileTypes: true })
      
      for (const file of files) {
        const fullPath = path.join(dir, file.name)
        
        if (file.isDirectory() && !file.name.startsWith('.')) {
          findComponents(fullPath)
        } else if (file.name.endsWith('.tsx') || file.name.endsWith('.jsx')) {
          components.push(fullPath)
          
          // Check if it's a mobile-specific component
          if (file.name.toLowerCase().includes('mobile')) {
            mobileComponents.push(fullPath)
          }
        }
      }
    } catch (error) {
      // Directory might not exist
    }
  }

  findComponents('./src/components')

  // Analyze components for mobile patterns
  components.forEach(file => {
    try {
      const content = fs.readFileSync(file, 'utf8')
      
      // Check for responsive patterns
      const hasUseMediaQuery = /useMediaQuery|useBreakpoint/.test(content)
      const hasMobileClasses = /mobile|sm:|md:|lg:|xl:/.test(content)
      const hasTouchEvents = /onTouch|touch/.test(content)
      
      if (hasMobileClasses) {
        log(`✅ ${file}: Has responsive classes`, 'green')
      }
      
      if (hasUseMediaQuery) {
        log(`✅ ${file}: Uses media query hooks`, 'green')
      }
      
      if (hasTouchEvents) {
        log(`✅ ${file}: Handles touch events`, 'green')
      }
      
    } catch (error) {
      issues.push(`${file}: Could not analyze component`)
    }
  })

  return { components, mobileComponents, issues }
}

function generateMobileTestReport(cssAnalysis, componentAnalysis) {
  const report = `# Mobile Responsiveness Test Report

Generated on: ${new Date().toISOString()}

## Summary

- **Total CSS Files**: ${cssAnalysis.cssFiles.length}
- **Total Components**: ${componentAnalysis.components.length}
- **Mobile-Specific Components**: ${componentAnalysis.mobileComponents.length}
- **Issues Found**: ${cssAnalysis.issues.length + componentAnalysis.issues.length}

## Responsive Design Analysis

### CSS Files Analyzed
${cssAnalysis.cssFiles.map(file => `- ${file}`).join('\n')}

### Mobile-Specific Components
${componentAnalysis.mobileComponents.map(file => `- ${file}`).join('\n')}

## Issues Found

### CSS Issues
${cssAnalysis.issues.map(issue => `- ❌ ${issue}`).join('\n')}

### Component Issues  
${componentAnalysis.issues.map(issue => `- ❌ ${issue}`).join('\n')}

## Recommendations

### CSS Improvements
${cssAnalysis.recommendations.map(rec => `- 💡 ${rec}`).join('\n')}

## Test Viewports

The following viewports should be tested:

${viewports.map(vp => `- **${vp.name}**: ${vp.width}x${vp.height}px (${vp.type})`).join('\n')}

## Pages to Test

${testPages.map(page => `- ${page}`).join('\n')}

## Mobile Features Checklist

${Object.entries(mobileFeatures).map(([key, feature]) => `
### ${key}
- **Description**: ${feature.description}
- **Selector**: ${feature.selector || 'N/A'}
- **Requirements**: ${feature.minSize ? `Min size: ${feature.minSize}px` : feature.maxWidth ? `Max width: ${feature.maxWidth}px` : 'See description'}
`).join('\n')}

## Manual Testing Steps

### 1. Responsive Layout Testing
1. Open the application in a browser
2. Use browser dev tools to test each viewport size
3. Verify layouts adapt correctly at breakpoints
4. Check for horizontal scrolling issues
5. Ensure content remains readable and accessible

### 2. Touch Interaction Testing
1. Test on actual mobile devices
2. Verify touch targets are large enough (44px minimum)
3. Test swipe gestures where applicable
4. Check form input behavior on mobile keyboards
5. Verify modal and dropdown interactions

### 3. Performance Testing
1. Test loading times on mobile networks
2. Check image optimization and lazy loading
3. Verify smooth scrolling and animations
4. Test offline functionality if applicable

### 4. Accessibility Testing
1. Test with screen readers on mobile
2. Verify keyboard navigation works
3. Check color contrast ratios
4. Test with different font sizes

## Automated Testing Commands

\`\`\`bash
# Run mobile-specific tests
pnpm test:mobile

# Run responsive design tests
pnpm test:responsive

# Run accessibility tests
pnpm test:a11y
\`\`\`

## Production Readiness Status

${cssAnalysis.issues.length === 0 && componentAnalysis.issues.length === 0 ? 
  '✅ **READY** - No critical mobile responsiveness issues found' :
  '⚠️  **NEEDS ATTENTION** - Issues found that should be addressed'}

## Next Steps

1. Fix any identified CSS and component issues
2. Perform manual testing on actual devices
3. Run automated responsive design tests
4. Validate touch interactions and gestures
5. Test performance on mobile networks
6. Verify accessibility compliance

---

*This report should be updated after implementing fixes and retesting.*
`

  return report
}

function main() {
  log('📱 Mobile Responsiveness Analysis', 'bold')
  
  // Analyze CSS
  const cssAnalysis = analyzeResponsiveCSS()
  
  // Analyze components
  const componentAnalysis = analyzeComponentStructure()
  
  // Generate report
  const report = generateMobileTestReport(cssAnalysis, componentAnalysis)
  fs.writeFileSync('MOBILE_RESPONSIVENESS_REPORT.md', report)
  
  log('\n📝 Mobile responsiveness report generated: MOBILE_RESPONSIVENESS_REPORT.md', 'green')
  
  // Summary
  const totalIssues = cssAnalysis.issues.length + componentAnalysis.issues.length
  
  log('\n📊 Summary:', 'bold')
  log(`CSS Files: ${cssAnalysis.cssFiles.length}`, 'cyan')
  log(`Components: ${componentAnalysis.components.length}`, 'cyan')
  log(`Mobile Components: ${componentAnalysis.mobileComponents.length}`, 'cyan')
  log(`Issues: ${totalIssues}`, totalIssues === 0 ? 'green' : 'yellow')
  
  if (totalIssues === 0) {
    log('\n✅ No critical mobile responsiveness issues found!', 'green')
  } else {
    log(`\n⚠️  Found ${totalIssues} issues that should be addressed`, 'yellow')
  }
  
  log('\n📖 See MOBILE_RESPONSIVENESS_REPORT.md for detailed analysis', 'cyan')
}

if (require.main === module) {
  main()
}

module.exports = { analyzeResponsiveCSS, analyzeComponentStructure, generateMobileTestReport }
