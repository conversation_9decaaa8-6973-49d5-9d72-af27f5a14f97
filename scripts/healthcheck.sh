#!/bin/sh

# Docker Health Check Script
# Tests if the application is responding correctly

set -e

# Configuration
HOST=${HOSTNAME:-localhost}
PORT=${PORT:-3000}
TIMEOUT=${HEALTH_CHECK_TIMEOUT:-10}

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

log() {
    echo "$(date '+%Y-%m-%d %H:%M:%S') - $1"
}

# Test basic HTTP response
test_http() {
    log "Testing HTTP endpoint..."
    
    # Use wget if available, otherwise curl
    if command -v wget >/dev/null 2>&1; then
        if wget --quiet --timeout=$TIMEOUT --tries=1 --spider "http://${HOST}:${PORT}/api/health" 2>/dev/null; then
            log "${GREEN}✓ HTTP endpoint responding${NC}"
            return 0
        else
            log "${RED}✗ HTTP endpoint not responding${NC}"
            return 1
        fi
    elif command -v curl >/dev/null 2>&1; then
        if curl --silent --fail --max-time $TIMEOUT "http://${HOST}:${PORT}/api/health" >/dev/null 2>&1; then
            log "${GREEN}✓ HTTP endpoint responding${NC}"
            return 0
        else
            log "${RED}✗ HTTP endpoint not responding${NC}"
            return 1
        fi
    else
        log "${YELLOW}⚠ Neither wget nor curl available, skipping HTTP test${NC}"
        return 0
    fi
}

# Test database connectivity
test_database() {
    log "Testing database connectivity..."
    
    if command -v curl >/dev/null 2>&1; then
        response=$(curl --silent --max-time $TIMEOUT "http://${HOST}:${PORT}/api/health/db" 2>/dev/null || echo "")
        
        if echo "$response" | grep -q '"status":"healthy"'; then
            log "${GREEN}✓ Database connectivity healthy${NC}"
            return 0
        else
            log "${RED}✗ Database connectivity issues${NC}"
            return 1
        fi
    else
        log "${YELLOW}⚠ curl not available, skipping database test${NC}"
        return 0
    fi
}

# Test memory usage
test_memory() {
    log "Testing memory usage..."
    
    # Get memory info if available
    if [ -f /proc/meminfo ]; then
        mem_total=$(grep MemTotal /proc/meminfo | awk '{print $2}')
        mem_available=$(grep MemAvailable /proc/meminfo | awk '{print $2}')
        
        if [ "$mem_available" -gt 0 ] && [ "$mem_total" -gt 0 ]; then
            mem_usage_percent=$((100 - (mem_available * 100 / mem_total)))
            
            if [ "$mem_usage_percent" -lt 90 ]; then
                log "${GREEN}✓ Memory usage acceptable (${mem_usage_percent}%)${NC}"
                return 0
            else
                log "${YELLOW}⚠ High memory usage (${mem_usage_percent}%)${NC}"
                return 1
            fi
        fi
    fi
    
    log "${YELLOW}⚠ Cannot determine memory usage${NC}"
    return 0
}

# Main health check
main() {
    log "Starting health check..."
    
    # Track failures
    failures=0
    
    # Run tests
    test_http || failures=$((failures + 1))
    test_database || failures=$((failures + 1))
    test_memory || failures=$((failures + 1))
    
    # Summary
    if [ $failures -eq 0 ]; then
        log "${GREEN}✓ All health checks passed${NC}"
        exit 0
    else
        log "${RED}✗ $failures health check(s) failed${NC}"
        exit 1
    fi
}

# Run main function
main "$@"
