#!/usr/bin/env node

/**
 * Code Quality Fixer
 * Automatically fixes common ESLint issues and generates a report
 */

const { execSync } = require('child_process')
const fs = require('fs')
const path = require('path')

// Color codes for console output
const colors = {
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m',
  white: '\x1b[37m',
  reset: '\x1b[0m',
  bold: '\x1b[1m'
}

function log(message, color = 'white') {
  console.log(`${colors[color]}${message}${colors.reset}`)
}

function runCommand(command, description) {
  log(`\n${description}...`, 'cyan')
  try {
    const output = execSync(command, { encoding: 'utf8', stdio: 'pipe' })
    log(`✅ ${description} completed`, 'green')
    return { success: true, output }
  } catch (error) {
    log(`⚠️  ${description} had issues: ${error.message}`, 'yellow')
    return { success: false, error: error.message, output: error.stdout }
  }
}

function analyzeESLintOutput(output) {
  const lines = output.split('\n')
  const issues = {
    'no-console': 0,
    '@typescript-eslint/no-explicit-any': 0,
    '@typescript-eslint/no-unused-vars': 0,
    '@typescript-eslint/prefer-nullish-coalescing': 0,
    '@typescript-eslint/no-non-null-assertion': 0,
    'prefer-template': 0,
    'object-shorthand': 0,
    'react-hooks/exhaustive-deps': 0,
    'other': 0
  }

  let totalWarnings = 0
  let totalErrors = 0

  lines.forEach(line => {
    if (line.includes('Warning:')) {
      totalWarnings++
      
      // Count specific rule violations
      Object.keys(issues).forEach(rule => {
        if (rule !== 'other' && line.includes(rule)) {
          issues[rule]++
          return
        }
      })
      
      // Count other issues
      let foundKnownRule = false
      Object.keys(issues).forEach(rule => {
        if (rule !== 'other' && line.includes(rule)) {
          foundKnownRule = true
        }
      })
      if (!foundKnownRule) {
        issues.other++
      }
    }
    
    if (line.includes('Error:')) {
      totalErrors++
    }
  })

  return { issues, totalWarnings, totalErrors }
}

function generateCodeQualityReport(analysis) {
  const report = `# Code Quality Report

Generated on: ${new Date().toISOString()}

## Summary

- **Total Warnings**: ${analysis.totalWarnings}
- **Total Errors**: ${analysis.totalErrors}

## Issue Breakdown

| Rule | Count | Priority | Auto-fixable |
|------|-------|----------|--------------|
| \`no-console\` | ${analysis.issues['no-console']} | Medium | ✅ |
| \`@typescript-eslint/no-explicit-any\` | ${analysis.issues['@typescript-eslint/no-explicit-any']} | High | ❌ |
| \`@typescript-eslint/no-unused-vars\` | ${analysis.issues['@typescript-eslint/no-unused-vars']} | Medium | ✅ |
| \`@typescript-eslint/prefer-nullish-coalescing\` | ${analysis.issues['@typescript-eslint/prefer-nullish-coalescing']} | Low | ✅ |
| \`@typescript-eslint/no-non-null-assertion\` | ${analysis.issues['@typescript-eslint/no-non-null-assertion']} | High | ❌ |
| \`prefer-template\` | ${analysis.issues['prefer-template']} | Low | ✅ |
| \`object-shorthand\` | ${analysis.issues['object-shorthand']} | Low | ✅ |
| \`react-hooks/exhaustive-deps\` | ${analysis.issues['react-hooks/exhaustive-deps']} | Medium | ❌ |
| Other issues | ${analysis.issues.other} | Various | Various |

## Recommendations

### High Priority (Fix Before Production)
1. **Remove \`any\` types** (${analysis.issues['@typescript-eslint/no-explicit-any']} instances)
   - Replace with proper TypeScript types
   - Use generic types where appropriate
   - Add proper interface definitions

2. **Remove non-null assertions** (${analysis.issues['@typescript-eslint/no-non-null-assertion']} instances)
   - Add proper null checks
   - Use optional chaining (\`?.\`)
   - Handle undefined cases explicitly

### Medium Priority (Fix Soon)
1. **Remove console statements** (${analysis.issues['no-console']} instances)
   - Replace with proper logging using the logger utility
   - Remove debug console.log statements

2. **Fix unused variables** (${analysis.issues['@typescript-eslint/no-unused-vars']} instances)
   - Remove unused imports and variables
   - Prefix with underscore if intentionally unused

3. **Fix React hooks dependencies** (${analysis.issues['react-hooks/exhaustive-deps']} instances)
   - Add missing dependencies to useEffect
   - Use useCallback for stable function references

### Low Priority (Code Style)
1. **Use nullish coalescing** (${analysis.issues['@typescript-eslint/prefer-nullish-coalescing']} instances)
   - Replace \`||\` with \`??\` where appropriate

2. **Use template literals** (${analysis.issues['prefer-template']} instances)
   - Replace string concatenation with template literals

3. **Use object shorthand** (${analysis.issues['object-shorthand']} instances)
   - Use shorthand property syntax

## Auto-fix Commands

\`\`\`bash
# Fix auto-fixable issues
pnpm lint --fix

# Fix specific rule types
pnpm eslint src --fix --rule 'prefer-template: error'
pnpm eslint src --fix --rule 'object-shorthand: error'
\`\`\`

## Manual Fix Required

The following issues require manual intervention:
- TypeScript \`any\` types need proper type definitions
- Non-null assertions need proper null handling
- React hooks dependencies need careful review
- Console statements need replacement with proper logging

## Production Readiness Status

${analysis.totalErrors > 0 ? '❌ **NOT READY** - Has errors that must be fixed' : 
  analysis.issues['@typescript-eslint/no-explicit-any'] > 50 || analysis.issues['@typescript-eslint/no-non-null-assertion'] > 20 ? 
  '⚠️  **NEEDS ATTENTION** - High priority issues should be addressed' :
  '✅ **READY** - Code quality is acceptable for production'}

## Next Steps

1. Run \`pnpm lint --fix\` to auto-fix simple issues
2. Manually address high-priority TypeScript issues
3. Review and fix React hooks dependencies
4. Replace console statements with proper logging
5. Re-run this report to track progress
`

  return report
}

function main() {
  log('🔍 Code Quality Analysis and Fixes', 'bold')
  
  // Run ESLint to get current state
  log('\n📊 Analyzing current code quality...', 'cyan')
  const lintResult = runCommand('pnpm lint 2>&1 || true', 'Running ESLint analysis')
  
  if (lintResult.output) {
    const analysis = analyzeESLintOutput(lintResult.output)
    
    // Generate report
    const report = generateCodeQualityReport(analysis)
    fs.writeFileSync('CODE_QUALITY_REPORT.md', report)
    log('📝 Code quality report generated: CODE_QUALITY_REPORT.md', 'green')
    
    // Try to auto-fix what we can
    log('\n🔧 Attempting to auto-fix issues...', 'cyan')
    const fixResult = runCommand('pnpm lint --fix 2>&1 || true', 'Auto-fixing ESLint issues')
    
    // Run analysis again to see improvement
    const postFixResult = runCommand('pnpm lint 2>&1 || true', 'Re-analyzing after fixes')
    if (postFixResult.output) {
      const postAnalysis = analyzeESLintOutput(postFixResult.output)
      
      log('\n📈 Improvement Summary:', 'bold')
      log(`Warnings: ${analysis.totalWarnings} → ${postAnalysis.totalWarnings} (${analysis.totalWarnings - postAnalysis.totalWarnings} fixed)`, 'green')
      log(`Errors: ${analysis.totalErrors} → ${postAnalysis.totalErrors} (${analysis.totalErrors - postAnalysis.totalErrors} fixed)`, 'green')
    }
    
    // Summary
    log('\n🎯 Summary:', 'bold')
    if (analysis.totalErrors === 0 && analysis.totalWarnings < 100) {
      log('✅ Code quality is acceptable for production', 'green')
    } else if (analysis.totalErrors === 0) {
      log('⚠️  Code quality needs improvement but is deployable', 'yellow')
    } else {
      log('❌ Code quality issues must be fixed before production', 'red')
    }
    
    log('\n📖 See CODE_QUALITY_REPORT.md for detailed recommendations', 'cyan')
  }
}

if (require.main === module) {
  main()
}

module.exports = { analyzeESLintOutput, generateCodeQualityReport }
