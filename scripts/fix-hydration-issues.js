#!/usr/bin/env node

/**
 * Hydration Issues Fixer
 * Identifies and fixes common hydration mismatches in React/Next.js applications
 */

const fs = require('fs')
const path = require('path')

// Color codes for console output
const colors = {
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m',
  white: '\x1b[37m',
  reset: '\x1b[0m',
  bold: '\x1b[1m'
}

function log(message, color = 'white') {
  console.log(`${colors[color]}${message}${colors.reset}`)
}

// Common hydration issue patterns
const hydrationIssues = [
  {
    name: 'Math.random() usage',
    pattern: /Math\.random\(\)/g,
    description: 'Math.random() generates different values on server vs client',
    severity: 'high',
    fix: 'Replace with deterministic calculations or use useEffect'
  },
  {
    name: 'Date.now() usage',
    pattern: /Date\.now\(\)/g,
    description: 'Date.now() returns different values on server vs client',
    severity: 'high',
    fix: 'Use static dates or client-side only calculations'
  },
  {
    name: 'new Date() without parameters',
    pattern: /new Date\(\)(?!\.[a-zA-Z])/g,
    description: 'new Date() returns different values on server vs client',
    severity: 'high',
    fix: 'Use static dates or client-side only calculations'
  },
  {
    name: 'toLocaleDateString without options',
    pattern: /\.toLocaleDateString\(\s*['"][^'"]*['"]\s*\)/g,
    description: 'toLocaleDateString may render differently due to timezone differences',
    severity: 'medium',
    fix: 'Use consistent date formatting with explicit options'
  },
  {
    name: 'window object usage',
    pattern: /(?<!typeof\s+)window\./g,
    description: 'window object is not available during SSR',
    severity: 'high',
    fix: 'Add typeof window !== "undefined" check or use useEffect'
  },
  {
    name: 'localStorage usage',
    pattern: /localStorage\./g,
    description: 'localStorage is not available during SSR',
    severity: 'high',
    fix: 'Add typeof window !== "undefined" check or use useEffect'
  },
  {
    name: 'sessionStorage usage',
    pattern: /sessionStorage\./g,
    description: 'sessionStorage is not available during SSR',
    severity: 'high',
    fix: 'Add typeof window !== "undefined" check or use useEffect'
  },
  {
    name: 'document object usage',
    pattern: /(?<!typeof\s+)document\./g,
    description: 'document object is not available during SSR',
    severity: 'high',
    fix: 'Add typeof document !== "undefined" check or use useEffect'
  },
  {
    name: 'Dynamic class names without guards',
    pattern: /className=\{[^}]*\?[^}]*:/g,
    description: 'Dynamic class names may cause hydration mismatches',
    severity: 'medium',
    fix: 'Ensure consistent class name generation or use client-side only'
  }
]

function scanFile(filePath) {
  try {
    const content = fs.readFileSync(filePath, 'utf8')
    const issues = []

    hydrationIssues.forEach(issue => {
      const matches = content.match(issue.pattern)
      if (matches) {
        matches.forEach(match => {
          const lines = content.split('\n')
          const lineNumber = lines.findIndex(line => line.includes(match)) + 1
          
          issues.push({
            file: filePath,
            line: lineNumber,
            issue: issue.name,
            match: match.trim(),
            severity: issue.severity,
            description: issue.description,
            fix: issue.fix
          })
        })
      }
    })

    return issues
  } catch (error) {
    return []
  }
}

function scanDirectory(dir, extensions = ['.tsx', '.ts', '.jsx', '.js']) {
  const allIssues = []

  function scanRecursive(currentDir) {
    try {
      const files = fs.readdirSync(currentDir, { withFileTypes: true })
      
      for (const file of files) {
        const fullPath = path.join(currentDir, file.name)
        
        if (file.isDirectory() && !file.name.startsWith('.') && file.name !== 'node_modules') {
          scanRecursive(fullPath)
        } else if (file.isFile() && extensions.some(ext => file.name.endsWith(ext))) {
          const issues = scanFile(fullPath)
          allIssues.push(...issues)
        }
      }
    } catch (error) {
      // Directory might not exist or be accessible
    }
  }

  scanRecursive(dir)
  return allIssues
}

function generateHydrationReport(issues) {
  const timestamp = new Date().toISOString()
  
  // Group issues by severity
  const highSeverity = issues.filter(issue => issue.severity === 'high')
  const mediumSeverity = issues.filter(issue => issue.severity === 'medium')
  const lowSeverity = issues.filter(issue => issue.severity === 'low')
  
  // Group issues by type
  const issuesByType = issues.reduce((acc, issue) => {
    if (!acc[issue.issue]) {
      acc[issue.issue] = []
    }
    acc[issue.issue].push(issue)
    return acc
  }, {})

  const report = `# Hydration Issues Report

Generated on: ${timestamp}

## Summary

- **Total Issues**: ${issues.length}
- **High Severity**: ${highSeverity.length}
- **Medium Severity**: ${mediumSeverity.length}
- **Low Severity**: ${lowSeverity.length}

## Issues by Severity

### 🔴 High Severity Issues (${highSeverity.length})

${highSeverity.length === 0 ? 'No high severity issues found! ✅' : 
  highSeverity.map(issue => `
**${issue.issue}**
- File: \`${issue.file}:${issue.line}\`
- Code: \`${issue.match}\`
- Description: ${issue.description}
- Fix: ${issue.fix}
`).join('\n')}

### 🟡 Medium Severity Issues (${mediumSeverity.length})

${mediumSeverity.length === 0 ? 'No medium severity issues found! ✅' : 
  mediumSeverity.map(issue => `
**${issue.issue}**
- File: \`${issue.file}:${issue.line}\`
- Code: \`${issue.match}\`
- Description: ${issue.description}
- Fix: ${issue.fix}
`).join('\n')}

## Issues by Type

${Object.entries(issuesByType).map(([type, typeIssues]) => `
### ${type} (${typeIssues.length} occurrences)

${typeIssues.map(issue => `- \`${issue.file}:${issue.line}\` - \`${issue.match}\``).join('\n')}
`).join('\n')}

## Recommended Fixes

### 1. Add Hydration Guards

For client-side only code, use the mounted pattern:

\`\`\`tsx
const [mounted, setMounted] = useState(false)

useEffect(() => {
  setMounted(true)
}, [])

if (!mounted) return null // or loading state

// Client-side only code here
\`\`\`

### 2. Fix Date Handling

Replace inconsistent date formatting:

\`\`\`tsx
// ❌ Bad - may cause hydration mismatch
const date = new Date().toLocaleDateString('zh-CN')

// ✅ Good - consistent formatting
const formatDate = (dateString: string) => {
  if (!mounted) return ''
  return new Date(dateString).toLocaleDateString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit'
  })
}
\`\`\`

### 3. Replace Random Values

Replace Math.random() with deterministic calculations:

\`\`\`tsx
// ❌ Bad - different values on server vs client
const growth = Math.round(Math.random() * 20)

// ✅ Good - deterministic calculation
const growth = Math.min(Math.max(Math.floor((data.length * 0.1) + 5), 0), 50)
\`\`\`

### 4. Guard Browser APIs

Add proper checks for browser-only APIs:

\`\`\`tsx
// ❌ Bad - will fail during SSR
const value = localStorage.getItem('key')

// ✅ Good - properly guarded
const value = typeof window !== 'undefined' ? localStorage.getItem('key') : null
\`\`\`

## Production Readiness

${highSeverity.length === 0 ? 
  '✅ **READY** - No critical hydration issues found' :
  '❌ **NOT READY** - Critical hydration issues must be fixed before production'}

${issues.length === 0 ? 
  '\n🎉 **Excellent!** No hydration issues detected. Your application should have smooth client-side hydration.' :
  `\n⚠️ **Action Required**: ${issues.length} hydration issues found. Fix high severity issues before production deployment.`}

---

*Run \`pnpm fix:hydration\` to re-scan after making fixes.*
`

  return report
}

function main() {
  log('🔍 Scanning for Hydration Issues', 'bold')
  
  // Scan source directories
  const srcIssues = scanDirectory('./src')
  const componentIssues = scanDirectory('./components')
  const pageIssues = scanDirectory('./pages')
  
  const allIssues = [...srcIssues, ...componentIssues, ...pageIssues]
  
  log(`\n📊 Scan Results:`, 'cyan')
  log(`Total files scanned: ${new Set(allIssues.map(i => i.file)).size}`)
  log(`Total issues found: ${allIssues.length}`)
  
  // Group by severity
  const highSeverity = allIssues.filter(issue => issue.severity === 'high')
  const mediumSeverity = allIssues.filter(issue => issue.severity === 'medium')
  
  log(`High severity: ${highSeverity.length}`, highSeverity.length > 0 ? 'red' : 'green')
  log(`Medium severity: ${mediumSeverity.length}`, mediumSeverity.length > 0 ? 'yellow' : 'green')
  
  // Generate report
  const report = generateHydrationReport(allIssues)
  fs.writeFileSync('HYDRATION_ISSUES_REPORT.md', report)
  
  log('\n📝 Hydration issues report generated: HYDRATION_ISSUES_REPORT.md', 'green')
  
  // Summary
  if (allIssues.length === 0) {
    log('\n🎉 No hydration issues found! Your application should hydrate smoothly.', 'green')
  } else {
    log(`\n⚠️  Found ${allIssues.length} potential hydration issues`, 'yellow')
    
    if (highSeverity.length > 0) {
      log(`❌ ${highSeverity.length} high severity issues must be fixed before production`, 'red')
    }
    
    log('\n📖 See HYDRATION_ISSUES_REPORT.md for detailed analysis and fixes', 'cyan')
  }
  
  // Exit with appropriate code
  process.exit(highSeverity.length > 0 ? 1 : 0)
}

if (require.main === module) {
  main()
}

module.exports = { scanFile, scanDirectory, generateHydrationReport }
