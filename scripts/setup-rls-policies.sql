-- Medical Aesthetics CRM Row Level Security (RLS) Policies
-- This script sets up comprehensive RLS policies for production security

-- =================================================================
-- 1. ENABLE RLS ON ALL TABLES
-- =================================================================

-- Enable RLS on all tables
ALTER TABLE clients ENABLE ROW LEVEL SECURITY;
ALTER TABLE treatments ENABLE ROW LEVEL SECURITY;
ALTER TABLE appointments ENABLE ROW LEVEL SECURITY;
ALTER TABLE invoices ENABLE ROW LEVEL SECURITY;
ALTER TABLE invoice_items ENABLE ROW LEVEL SECURITY;
ALTER TABLE payments ENABLE ROW LEVEL SECURITY;
ALTER TABLE contact_logs ENABLE ROW LEVEL SECURITY;

-- =================================================================
-- 2. CREATE HELPER FUNCTIONS
-- =================================================================

-- Function to check if user is authenticated
CREATE OR REPLACE FUNCTION auth.is_authenticated()
RETURNS BOOLEAN AS $$
BEGIN
  RETURN auth.uid() IS NOT NULL;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to get current user's role
CREATE OR REPLACE FUNCTION auth.get_user_role()
RETURNS TEXT AS $$
BEGIN
  RETURN COALESCE(
    (auth.jwt() ->> 'user_role')::TEXT,
    'staff'  -- Default role
  );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to check if user is admin
CREATE OR REPLACE FUNCTION auth.is_admin()
RETURNS BOOLEAN AS $$
BEGIN
  RETURN auth.get_user_role() = 'admin';
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to check if user is staff or admin
CREATE OR REPLACE FUNCTION auth.is_staff_or_admin()
RETURNS BOOLEAN AS $$
BEGIN
  RETURN auth.get_user_role() IN ('staff', 'admin');
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- =================================================================
-- 3. CLIENTS TABLE POLICIES
-- =================================================================

-- Drop existing policies if they exist
DROP POLICY IF EXISTS "clients_select_policy" ON clients;
DROP POLICY IF EXISTS "clients_insert_policy" ON clients;
DROP POLICY IF EXISTS "clients_update_policy" ON clients;
DROP POLICY IF EXISTS "clients_delete_policy" ON clients;

-- Clients: Staff and admin can view all clients
CREATE POLICY "clients_select_policy" ON clients
  FOR SELECT
  USING (auth.is_staff_or_admin());

-- Clients: Staff and admin can insert new clients
CREATE POLICY "clients_insert_policy" ON clients
  FOR INSERT
  WITH CHECK (auth.is_staff_or_admin());

-- Clients: Staff and admin can update clients
CREATE POLICY "clients_update_policy" ON clients
  FOR UPDATE
  USING (auth.is_staff_or_admin())
  WITH CHECK (auth.is_staff_or_admin());

-- Clients: Only admin can delete clients
CREATE POLICY "clients_delete_policy" ON clients
  FOR DELETE
  USING (auth.is_admin());

-- =================================================================
-- 4. TREATMENTS TABLE POLICIES
-- =================================================================

-- Drop existing policies if they exist
DROP POLICY IF EXISTS "treatments_select_policy" ON treatments;
DROP POLICY IF EXISTS "treatments_insert_policy" ON treatments;
DROP POLICY IF EXISTS "treatments_update_policy" ON treatments;
DROP POLICY IF EXISTS "treatments_delete_policy" ON treatments;

-- Treatments: Staff and admin can view all treatments
CREATE POLICY "treatments_select_policy" ON treatments
  FOR SELECT
  USING (auth.is_staff_or_admin());

-- Treatments: Only admin can insert new treatments
CREATE POLICY "treatments_insert_policy" ON treatments
  FOR INSERT
  WITH CHECK (auth.is_admin());

-- Treatments: Only admin can update treatments
CREATE POLICY "treatments_update_policy" ON treatments
  FOR UPDATE
  USING (auth.is_admin())
  WITH CHECK (auth.is_admin());

-- Treatments: Only admin can delete treatments
CREATE POLICY "treatments_delete_policy" ON treatments
  FOR DELETE
  USING (auth.is_admin());

-- =================================================================
-- 5. APPOINTMENTS TABLE POLICIES
-- =================================================================

-- Drop existing policies if they exist
DROP POLICY IF EXISTS "appointments_select_policy" ON appointments;
DROP POLICY IF EXISTS "appointments_insert_policy" ON appointments;
DROP POLICY IF EXISTS "appointments_update_policy" ON appointments;
DROP POLICY IF EXISTS "appointments_delete_policy" ON appointments;

-- Appointments: Staff and admin can view all appointments
CREATE POLICY "appointments_select_policy" ON appointments
  FOR SELECT
  USING (auth.is_staff_or_admin());

-- Appointments: Staff and admin can insert new appointments
CREATE POLICY "appointments_insert_policy" ON appointments
  FOR INSERT
  WITH CHECK (auth.is_staff_or_admin());

-- Appointments: Staff and admin can update appointments
CREATE POLICY "appointments_update_policy" ON appointments
  FOR UPDATE
  USING (auth.is_staff_or_admin())
  WITH CHECK (auth.is_staff_or_admin());

-- Appointments: Staff and admin can delete appointments
CREATE POLICY "appointments_delete_policy" ON appointments
  FOR DELETE
  USING (auth.is_staff_or_admin());

-- =================================================================
-- 6. INVOICES TABLE POLICIES
-- =================================================================

-- Drop existing policies if they exist
DROP POLICY IF EXISTS "invoices_select_policy" ON invoices;
DROP POLICY IF EXISTS "invoices_insert_policy" ON invoices;
DROP POLICY IF EXISTS "invoices_update_policy" ON invoices;
DROP POLICY IF EXISTS "invoices_delete_policy" ON invoices;

-- Invoices: Staff and admin can view all invoices
CREATE POLICY "invoices_select_policy" ON invoices
  FOR SELECT
  USING (auth.is_staff_or_admin());

-- Invoices: Staff and admin can insert new invoices
CREATE POLICY "invoices_insert_policy" ON invoices
  FOR INSERT
  WITH CHECK (auth.is_staff_or_admin());

-- Invoices: Staff and admin can update invoices
CREATE POLICY "invoices_update_policy" ON invoices
  FOR UPDATE
  USING (auth.is_staff_or_admin())
  WITH CHECK (auth.is_staff_or_admin());

-- Invoices: Only admin can delete invoices
CREATE POLICY "invoices_delete_policy" ON invoices
  FOR DELETE
  USING (auth.is_admin());

-- =================================================================
-- 7. INVOICE ITEMS TABLE POLICIES
-- =================================================================

-- Drop existing policies if they exist
DROP POLICY IF EXISTS "invoice_items_select_policy" ON invoice_items;
DROP POLICY IF EXISTS "invoice_items_insert_policy" ON invoice_items;
DROP POLICY IF EXISTS "invoice_items_update_policy" ON invoice_items;
DROP POLICY IF EXISTS "invoice_items_delete_policy" ON invoice_items;

-- Invoice Items: Staff and admin can view all invoice items
CREATE POLICY "invoice_items_select_policy" ON invoice_items
  FOR SELECT
  USING (auth.is_staff_or_admin());

-- Invoice Items: Staff and admin can insert new invoice items
CREATE POLICY "invoice_items_insert_policy" ON invoice_items
  FOR INSERT
  WITH CHECK (auth.is_staff_or_admin());

-- Invoice Items: Staff and admin can update invoice items
CREATE POLICY "invoice_items_update_policy" ON invoice_items
  FOR UPDATE
  USING (auth.is_staff_or_admin())
  WITH CHECK (auth.is_staff_or_admin());

-- Invoice Items: Only admin can delete invoice items
CREATE POLICY "invoice_items_delete_policy" ON invoice_items
  FOR DELETE
  USING (auth.is_admin());

-- =================================================================
-- 8. PAYMENTS TABLE POLICIES
-- =================================================================

-- Drop existing policies if they exist
DROP POLICY IF EXISTS "payments_select_policy" ON payments;
DROP POLICY IF EXISTS "payments_insert_policy" ON payments;
DROP POLICY IF EXISTS "payments_update_policy" ON payments;
DROP POLICY IF EXISTS "payments_delete_policy" ON payments;

-- Payments: Staff and admin can view all payments
CREATE POLICY "payments_select_policy" ON payments
  FOR SELECT
  USING (auth.is_staff_or_admin());

-- Payments: Staff and admin can insert new payments
CREATE POLICY "payments_insert_policy" ON payments
  FOR INSERT
  WITH CHECK (auth.is_staff_or_admin());

-- Payments: Staff and admin can update payments
CREATE POLICY "payments_update_policy" ON payments
  FOR UPDATE
  USING (auth.is_staff_or_admin())
  WITH CHECK (auth.is_staff_or_admin());

-- Payments: Only admin can delete payments
CREATE POLICY "payments_delete_policy" ON payments
  FOR DELETE
  USING (auth.is_admin());

-- =================================================================
-- 9. CONTACT LOGS TABLE POLICIES
-- =================================================================

-- Drop existing policies if they exist
DROP POLICY IF EXISTS "contact_logs_select_policy" ON contact_logs;
DROP POLICY IF EXISTS "contact_logs_insert_policy" ON contact_logs;
DROP POLICY IF EXISTS "contact_logs_update_policy" ON contact_logs;
DROP POLICY IF EXISTS "contact_logs_delete_policy" ON contact_logs;

-- Contact Logs: Staff and admin can view all contact logs
CREATE POLICY "contact_logs_select_policy" ON contact_logs
  FOR SELECT
  USING (auth.is_staff_or_admin());

-- Contact Logs: Staff and admin can insert new contact logs
CREATE POLICY "contact_logs_insert_policy" ON contact_logs
  FOR INSERT
  WITH CHECK (auth.is_staff_or_admin());

-- Contact Logs: Staff and admin can update contact logs
CREATE POLICY "contact_logs_update_policy" ON contact_logs
  FOR UPDATE
  USING (auth.is_staff_or_admin())
  WITH CHECK (auth.is_staff_or_admin());

-- Contact Logs: Staff and admin can delete contact logs
CREATE POLICY "contact_logs_delete_policy" ON contact_logs
  FOR DELETE
  USING (auth.is_staff_or_admin());

-- =================================================================
-- 10. GRANT PERMISSIONS TO AUTHENTICATED USERS
-- =================================================================

-- Grant usage on schema
GRANT USAGE ON SCHEMA public TO authenticated;

-- Grant permissions on tables
GRANT SELECT, INSERT, UPDATE, DELETE ON ALL TABLES IN SCHEMA public TO authenticated;

-- Grant permissions on sequences
GRANT USAGE, SELECT ON ALL SEQUENCES IN SCHEMA public TO authenticated;

-- =================================================================
-- 11. SECURITY NOTES
-- =================================================================

/*
IMPORTANT SECURITY CONSIDERATIONS:

1. User Roles:
   - 'admin': Full access to all operations
   - 'staff': Can manage clients, appointments, invoices, payments, and contact logs
   - Default role is 'staff' if not specified

2. Authentication:
   - All policies require authentication via auth.uid()
   - Unauthenticated users have no access to any data

3. Role-based Access:
   - Admin role is required for:
     - Managing treatments (CRUD)
     - Deleting clients, invoices, invoice items, payments
   - Staff role can:
     - View and manage clients, appointments, invoices, payments, contact logs
     - Cannot delete sensitive financial records

4. Implementation:
   - User roles should be set in JWT claims during authentication
   - Use Supabase Auth with custom claims or user metadata
   - Implement proper role assignment in your application logic

5. Testing:
   - Test all policies with different user roles
   - Verify that unauthorized access is properly blocked
   - Test edge cases and error conditions
*/
