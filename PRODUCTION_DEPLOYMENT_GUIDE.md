# Medical Aesthetics CRM - Production Deployment Guide

## 🚀 Quick Start

This guide will help you deploy the Medical Aesthetics CRM to production with all necessary configurations.

## ✅ Pre-Deployment Checklist

### 1. Environment Setup

```bash
# Validate current environment
pnpm validate:env

# Generate environment template
pnpm generate:env
```

### 2. Required Environment Variables

Create a `.env.production` file with the following variables:

```bash
# Core Application
NODE_ENV=production
NEXT_PUBLIC_APP_URL=https://yourdomain.com

# Database (Supabase) - REQUIRED
NEXT_PUBLIC_SUPABASE_URL=https://your-project.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
SUPABASE_SERVICE_ROLE_KEY=your_supabase_service_role_key

# Security - REQUIRED
NEXTAUTH_SECRET=your-32-character-secret-here
ENCRYPTION_KEY=your-32-character-encryption-key
JWT_SECRET=your-jwt-secret-here

# Authentication (Clerk) - Optional for keyless mode
NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY=pk_live_...
CLERK_SECRET_KEY=sk_live_...

# Error Tracking (Sentry) - Recommended
NEXT_PUBLIC_SENTRY_DSN=https://****@****.ingest.sentry.io/****
NEXT_PUBLIC_SENTRY_ORG=your-org
NEXT_PUBLIC_SENTRY_PROJECT=your-project
SENTRY_AUTH_TOKEN=sntrys_****

# Email Configuration - Optional
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your-app-password

# Performance - Optional
REDIS_URL=redis://localhost:6379
LOG_LEVEL=info
```

### 3. Database Setup

```sql
-- Run database validation
\i scripts/validate-database.sql

-- Apply RLS policies
\i scripts/setup-rls-policies.sql
```

### 4. Build and Test

```bash
# Install dependencies
pnpm install

# Run tests
pnpm test:unit

# Build for production
pnpm build:prod

# Validate build
pnpm start:prod
```

## 🔧 Detailed Configuration

### Supabase Configuration

1. **Create Supabase Project**
   - Go to [supabase.com](https://supabase.com)
   - Create a new project
   - Note down the project URL and API keys

2. **Database Schema**
   ```sql
   -- Run the schema validation script
   \i scripts/validate-database.sql
   
   -- Apply RLS policies for security
   \i scripts/setup-rls-policies.sql
   ```

3. **Row Level Security**
   - Enable RLS on all tables
   - Configure user roles (admin, staff)
   - Test access permissions

### Authentication Setup

#### Option 1: Clerk (Recommended)
1. Create account at [clerk.com](https://clerk.com)
2. Create new application
3. Configure authentication methods
4. Set up user roles and permissions

#### Option 2: Keyless Mode
- Leave Clerk environment variables empty
- Application will work in keyless mode
- Click the Clerk popup to claim your application later

### Error Tracking (Sentry)

1. **Setup Sentry**
   ```bash
   # Create account at sentry.io
   # Create new Next.js project
   # Get DSN and organization details
   ```

2. **Configure Source Maps**
   ```bash
   # Set SENTRY_AUTH_TOKEN for source map uploads
   pnpm build:prod
   ```

### Email Configuration

For appointment reminders and notifications:

```bash
# Gmail example
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your-app-password  # Use App Password, not regular password
```

## 🚀 Deployment Options

### Option 1: Vercel (Recommended)

1. **Connect Repository**
   ```bash
   # Push to GitHub
   git push origin main
   
   # Connect to Vercel
   # Import project from GitHub
   ```

2. **Environment Variables**
   - Add all production environment variables in Vercel dashboard
   - Use Vercel's environment variable management

3. **Deploy**
   ```bash
   # Automatic deployment on push
   # Or manual deployment from Vercel dashboard
   ```

### Option 2: Docker Deployment

1. **Build Docker Image**
   ```bash
   # Build production image
   docker build -t medical-crm .
   
   # Run container
   docker run -p 3000:3000 --env-file .env.production medical-crm
   ```

2. **Docker Compose**
   ```yaml
   version: '3.8'
   services:
     app:
       build: .
       ports:
         - "3000:3000"
       env_file:
         - .env.production
   ```

### Option 3: Traditional Server

1. **Server Requirements**
   - Node.js 18+ 
   - PM2 for process management
   - Nginx for reverse proxy
   - SSL certificate

2. **Deployment Script**
   ```bash
   # Build application
   pnpm build:prod
   
   # Start with PM2
   pm2 start ecosystem.config.js
   
   # Configure Nginx
   sudo nginx -s reload
   ```

## 🔒 Security Considerations

### 1. Environment Variables
- Never commit `.env` files to version control
- Use strong, unique secrets (32+ characters)
- Rotate secrets regularly
- Use environment-specific configurations

### 2. Database Security
- Enable Row Level Security (RLS)
- Use service role key only on server-side
- Implement proper user roles and permissions
- Regular security audits

### 3. Application Security
- HTTPS only in production
- Proper CORS configuration
- Rate limiting on APIs
- Input validation and sanitization

### 4. Monitoring
- Set up error tracking (Sentry)
- Monitor performance metrics
- Log security events
- Set up alerts for critical issues

## 📊 Performance Optimization

### 1. Caching
```bash
# Enable Redis caching
REDIS_URL=redis://localhost:6379
ENABLE_CACHE=true
```

### 2. CDN Configuration
- Configure CDN for static assets
- Enable image optimization
- Use proper cache headers

### 3. Database Optimization
- Create recommended indexes (see validate-database.sql)
- Monitor query performance
- Implement connection pooling

## 🔍 Monitoring and Maintenance

### 1. Health Checks
```bash
# Application health endpoint
curl https://yourdomain.com/api/health

# Database connectivity
curl https://yourdomain.com/api/health/db
```

### 2. Logging
```bash
# Set appropriate log level
LOG_LEVEL=info  # debug, info, warn, error

# Monitor logs
tail -f /var/log/medical-crm/app.log
```

### 3. Backup Strategy
- Automated daily database backups
- File storage backups
- Configuration backups
- Test restore procedures

## 🆘 Troubleshooting

### Common Issues

1. **Build Failures**
   ```bash
   # Clear cache and rebuild
   rm -rf .next
   pnpm build
   ```

2. **Database Connection Issues**
   ```bash
   # Validate Supabase credentials
   pnpm validate:env
   
   # Test database connection
   node -e "console.log(process.env.NEXT_PUBLIC_SUPABASE_URL)"
   ```

3. **Authentication Issues**
   ```bash
   # Check Clerk configuration
   # Verify redirect URLs
   # Test in incognito mode
   ```

### Support Contacts
- Technical Support: [your-support-email]
- Database Issues: [your-db-admin-email]
- Infrastructure: [your-infra-team-email]

## 📚 Additional Resources

- [Next.js Deployment Documentation](https://nextjs.org/docs/deployment)
- [Supabase Production Checklist](https://supabase.com/docs/guides/platform/going-into-prod)
- [Clerk Production Guide](https://clerk.com/docs/deployments/overview)
- [Sentry Next.js Integration](https://docs.sentry.io/platforms/javascript/guides/nextjs/)

---

## 🎯 Success Criteria

Your deployment is successful when:

- ✅ All environment variables are properly configured
- ✅ Application builds and starts without errors
- ✅ Database connection is working
- ✅ Authentication is functioning
- ✅ All critical features are accessible
- ✅ Error tracking is active
- ✅ Performance is acceptable (< 3s page loads)
- ✅ Security measures are in place

**Need Help?** Check the troubleshooting section or contact support.
