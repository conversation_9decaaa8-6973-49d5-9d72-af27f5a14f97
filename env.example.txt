# =================================================================
# Medical Aesthetics CRM - Environment Configuration
# =================================================================
# PRODUCTION DEPLOYMENT CHECKLIST:
# 1. Set NODE_ENV=production
# 2. Configure all database credentials
# 3. Set up proper authentication keys
# 4. Configure monitoring and logging
# 5. Set up SSL certificates
# 6. Configure backup systems
# =================================================================

# =================================================================
# Environment Configuration
# =================================================================
NODE_ENV=development
NEXT_PUBLIC_APP_URL=http://localhost:3000

# =================================================================
# Database Configuration (Supabase)
# =================================================================
# CRITICAL: These must be configured for production
NEXT_PUBLIC_SUPABASE_URL=your_supabase_project_url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
SUPABASE_SERVICE_ROLE_KEY=your_supabase_service_role_key

# Database Connection Pool Settings (Production)
DATABASE_MAX_CONNECTIONS=20
DATABASE_IDLE_TIMEOUT=30000
DATABASE_CONNECTION_TIMEOUT=60000

# =================================================================
# Authentication Configuration (Clerk)
# =================================================================
# IMPORTANT: This template supports Clerk's keyless mode!
# You can start using the app immediately without any configuration.
# When you're ready to claim your application, simply click the Clerk
# popup at the bottom of the screen to get your API keys.

# Required: Clerk API Keys (Leave empty for keyless mode)
NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY=
CLERK_SECRET_KEY=

# Authentication Redirect URLs
NEXT_PUBLIC_CLERK_SIGN_IN_URL="/auth/sign-in"
NEXT_PUBLIC_CLERK_SIGN_UP_URL="/auth/sign-up"
NEXT_PUBLIC_CLERK_AFTER_SIGN_IN_URL="/dashboard/overview"
NEXT_PUBLIC_CLERK_AFTER_SIGN_UP_URL="/dashboard/overview"

# =================================================================
# Security Configuration
# =================================================================
# CRITICAL: Generate strong secrets for production
NEXTAUTH_SECRET=your_nextauth_secret_here
ENCRYPTION_KEY=your_32_character_encryption_key_here
JWT_SECRET=your_jwt_secret_here

# CORS Configuration
ALLOWED_ORIGINS=http://localhost:3000,https://yourdomain.com

# Rate Limiting
RATE_LIMIT_MAX_REQUESTS=100
RATE_LIMIT_WINDOW_MS=900000

# =================================================================
# Error Tracking Configuration (Sentry)
# =================================================================
# To set up Sentry error tracking:
# 1. Create an account at https://sentry.io
# 2. Create a new project for Next.js
# 3. Follow the setup instructions below

# Step 1: Sentry DSN (Required)
NEXT_PUBLIC_SENTRY_DSN=    #Example: https://****@****.ingest.sentry.io/****

# Step 2: Organization & Project Details
NEXT_PUBLIC_SENTRY_ORG=  # Example: acme-corp
NEXT_PUBLIC_SENTRY_PROJECT=  # Example: nextjs-dashboard

# Step 3: Sentry Auth Token
SENTRY_AUTH_TOKEN=    #Example: sntrys_************************************

# Step 4: Environment Control (Optional)
NEXT_PUBLIC_SENTRY_DISABLED=false

# =================================================================
# Email Configuration (Production)
# =================================================================
# Configure for appointment reminders and notifications
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your_app_password
EMAIL_FROM=<EMAIL>

# =================================================================
# File Storage Configuration
# =================================================================
# For client photos and documents
UPLOAD_MAX_SIZE=10485760  # 10MB
ALLOWED_FILE_TYPES=image/jpeg,image/png,image/webp,application/pdf

# =================================================================
# Payment Gateway Configuration (Production)
# =================================================================
# Alipay Configuration
ALIPAY_APP_ID=your_alipay_app_id
ALIPAY_PRIVATE_KEY=your_alipay_private_key
ALIPAY_PUBLIC_KEY=your_alipay_public_key

# WeChat Pay Configuration
WECHAT_APP_ID=your_wechat_app_id
WECHAT_MCH_ID=your_wechat_merchant_id
WECHAT_API_KEY=your_wechat_api_key

# =================================================================
# Monitoring and Logging
# =================================================================
LOG_LEVEL=info
ENABLE_REQUEST_LOGGING=true
ENABLE_PERFORMANCE_MONITORING=true

# Health Check Configuration
HEALTH_CHECK_TIMEOUT=5000
HEALTH_CHECK_INTERVAL=30000

# =================================================================
# Cache Configuration
# =================================================================
REDIS_URL=redis://localhost:6379
CACHE_TTL=3600
ENABLE_CACHE=true

# =================================================================
# Backup Configuration
# =================================================================
BACKUP_ENABLED=true
BACKUP_SCHEDULE="0 2 * * *"  # Daily at 2 AM
BACKUP_RETENTION_DAYS=30

# =================================================================
# Important Notes:
# =================================================================
# 1. Rename this file to '.env.local' for local development
# 2. For production, use '.env.production' or environment variables
# 3. Never commit actual environment files to version control
# 4. Make sure to replace all placeholder values with real ones
# 5. Keep your secret keys private and never share them
# 6. Use strong, unique passwords and keys for production
# 7. Enable SSL/TLS in production
# 8. Configure proper firewall rules
# 9. Set up monitoring and alerting
# 10. Implement proper backup and disaster recovery
